"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/events.tsx":
/*!***************************************!*\
  !*** ./src/app/ui/logbook/events.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Events; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _forms_vessel_rescue__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./forms/vessel-rescue */ \"(app-pages-browser)/./src/app/ui/logbook/forms/vessel-rescue.tsx\");\n/* harmony import */ var _forms_person_rescue__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./forms/person-rescue */ \"(app-pages-browser)/./src/app/ui/logbook/forms/person-rescue.tsx\");\n/* harmony import */ var _forms_restricted_visibility__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./forms/restricted-visibility */ \"(app-pages-browser)/./src/app/ui/logbook/forms/restricted-visibility.tsx\");\n/* harmony import */ var _forms_bar_crossing__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./forms/bar-crossing */ \"(app-pages-browser)/./src/app/ui/logbook/forms/bar-crossing.tsx\");\n/* harmony import */ var _forms_passenger_drop_facility__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./forms/passenger-drop-facility */ \"(app-pages-browser)/./src/app/ui/logbook/forms/passenger-drop-facility.tsx\");\n/* harmony import */ var _forms_tasking__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./forms/tasking */ \"(app-pages-browser)/./src/app/ui/logbook/forms/tasking.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _forms_crew_training_event__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./forms/crew-training-event */ \"(app-pages-browser)/./src/app/ui/logbook/forms/crew-training-event.tsx\");\n/* harmony import */ var _forms_supernumerary_event__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./forms/supernumerary-event */ \"(app-pages-browser)/./src/app/ui/logbook/forms/supernumerary-event.tsx\");\n/* harmony import */ var _forms_passenger_vehicle_pick_drop__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./forms/passenger-vehicle-pick-drop */ \"(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx\");\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! lodash/uniqueId */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/uniqueId.js\");\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var _forms_refuelling_bunkering__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./forms/refuelling-bunkering */ \"(app-pages-browser)/./src/app/ui/logbook/forms/refuelling-bunkering.tsx\");\n/* harmony import */ var _app_lib_vesselTypes__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/lib/vesselTypes */ \"(app-pages-browser)/./src/app/lib/vesselTypes.tsx\");\n/* harmony import */ var _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/app/lib/logbook-configuration */ \"(app-pages-browser)/./src/app/lib/logbook-configuration/index.ts\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _forms_InfringementNotices__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./forms/InfringementNotices */ \"(app-pages-browser)/./src/app/ui/logbook/forms/InfringementNotices.tsx\");\n/* harmony import */ var _forms_trip_update__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./forms/trip-update */ \"(app-pages-browser)/./src/app/ui/logbook/forms/trip-update.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_21__);\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/accordion */ \"(app-pages-browser)/./src/components/ui/accordion.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _radio_logs_schedule__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./radio-logs-schedule */ \"(app-pages-browser)/./src/app/ui/logbook/radio-logs-schedule.tsx\");\n/* harmony import */ var _incident_record_incident_record_form__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ../incident-record/incident-record-form */ \"(app-pages-browser)/./src/app/ui/incident-record/incident-record-form.tsx\");\n/* harmony import */ var _forms_pilot_transfer__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./forms/pilot-transfer */ \"(app-pages-browser)/./src/app/ui/logbook/forms/pilot-transfer.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Events(param) {\n    let { currentTrip, logBookConfig, updateTripReport, locked, geoLocations, tripReport, crewMembers, masterID, vessel, vessels, offline = false, setSelectedRow, setCurrentEventType, setCurrentStop, currentEventType, currentStop, tripReport_Stops, setTripReport_Stops, displayDangerousGoodsPvpd = false, displayDangerousGoodsPvpdSailing, setDisplayDangerousGoodsPvpd, setDisplayDangerousGoodsPvpdSailing, allPVPDDangerousGoods, setAllPVPDDangerousGoods, selectedDGRPVPD, setSelectedDGRPVPD, fuelLogs, logBookStartDate } = param;\n    var _currentTrip_tripReport_Stops, _currentTrip_tripEvents_nodes, _currentTrip_tripEvents, _currentTrip_tripReport_Stops_nodes, _currentTrip_tripReport_Stops1, _currentTrip_tripEvents_nodes1, _currentTrip_tripEvents1, _currentTrip_tripReport_Stops_nodes1, _currentTrip_tripReport_Stops2, _currentTrip_tripReport_Stops3;\n    _s();\n    const [events, setEvents] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [openEventModal, setOpenEventModal] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [taskingEvents, setTaskingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const [accordionValue, setAccordionValue] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const vesselID = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useSearchParams)().get(\"vesselID\") || \"0\";\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [edit_tripActivity, setEdit_tripActivity] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [displayRadioLogs, setDisplayRadioLogs] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [activityTypeOptions, setActivityTypeOptions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_30__.useBreakpoints)();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__.hasPermission)(process.env.EDIT_LOGBOOKENTRY_ACTIVITY || \"EDIT_LOGBOOKENTRY_ACTIVITY\", permissions)) {\n                setEdit_tripActivity(true);\n            } else {\n                setEdit_tripActivity(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        init_permissions();\n        initData();\n    }, [\n        permissions\n    ]);\n    const initData = ()=>{\n        var _logBookConfig_customisedLogBookComponents;\n        const combinedFields = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents.nodes.filter((section)=>section.componentClass === \"SeaLogs\\\\EventType_LogBookComponent\" || section.componentClass === \"EventType_LogBookComponent\").reduce((acc, section)=>{\n            acc = acc.concat(section.customisedComponentFields.nodes);\n            return acc;\n        }, []);\n        const hasRescueType = combinedFields === null || combinedFields === void 0 ? void 0 : combinedFields.find((field)=>field.fieldName === \"VesselRescue\" || field.fieldName === \"HumanRescue\");\n        if (logBookConfig) {\n            const eventList = hasRescueType ? combinedFields.filter((field)=>!hasParent(field) && field.status !== \"Off\") : combinedFields === null || combinedFields === void 0 ? void 0 : combinedFields.filter((field)=>!hasParent(field) && field.status !== \"Off\" && field.fieldName !== \"TaskingStartUnderway\" && field.fieldName !== \"TaskingOnScene\" && field.fieldName !== \"TaskingOnTow\" && field.fieldName !== \"TaskingPaused\" && field.fieldName !== \"TaskingResumed\" && field.fieldName !== \"TaskingComplete\" && field.fieldName !== \"DangerousGoodsSailing\");\n            const filteredEvents = eventList === null || eventList === void 0 ? void 0 : eventList.map((event)=>({\n                    label: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_2__.getFieldName)(event).replace(/([a-z])([A-Z])/g, \"$1 $2\").replace(\"Passenger Arrival\", \"Arrival\").replace(\"Passenger Departure\", \"Departure\"),\n                    value: event.fieldName\n                })).filter((event, index, self)=>index === self.findIndex((e)=>e.value === event.value)).filter((event)=>// event?.value !== 'VesselRescue' &&\n                // event?.value !== 'HumanRescue' &&\n                // event?.value !== 'Supernumerary' &&\n                !isTowingField(event.value)).filter((event)=>checkVesselType(event.value));\n            // Add Incident Record as a custom activity type\n            // Incident Record is available for all vessel types\n            filteredEvents.push({\n                label: \"Incident Record\",\n                value: \"IncidentRecord\"\n            });\n            // Add Infringement Notices as a custom activity type if vessel type allows it\n            // InfringementNotices is only available for vessel types 0 and 1\n            const vesselTypeID = _app_lib_vesselTypes__WEBPACK_IMPORTED_MODULE_16__[\"default\"].findIndex((type)=>type == (vessel === null || vessel === void 0 ? void 0 : vessel.vesselType));\n            if ([\n                0,\n                1\n            ].includes(vesselTypeID)) {\n                filteredEvents.push({\n                    label: \"Infringement Notices\",\n                    value: \"InfringementNotice\"\n                });\n            }\n            if (permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__.hasPermission)(\"RECORD_TRAINING\", permissions)) {\n                setEvents(sortFilteredEvents(filteredEvents));\n            } else {\n                var _filteredEvents_filter;\n                setEvents(sortFilteredEvents((_filteredEvents_filter = filteredEvents === null || filteredEvents === void 0 ? void 0 : filteredEvents.filter((event)=>event.value !== \"CrewTraining\")) !== null && _filteredEvents_filter !== void 0 ? _filteredEvents_filter : []));\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        initData();\n    }, [\n        logBookConfig\n    ]);\n    const checkVesselType = (field)=>{\n        const vesselTypeID = _app_lib_vesselTypes__WEBPACK_IMPORTED_MODULE_16__[\"default\"].findIndex((type)=>type == (vessel === null || vessel === void 0 ? void 0 : vessel.vesselType));\n        const config = _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_17__.SLALL_LogBookFields.find((localField)=>localField.componentClass === \"EventType_LogBookComponent\");\n        const isVesselType = config === null || config === void 0 ? void 0 : config.items.find((localField)=>field === localField.value && localField.vesselType.includes(vesselTypeID));\n        return isVesselType ? true : false;\n    };\n    const sortFilteredEvents = (events)=>{\n        var _currentTrip_tripEvents_nodes, _currentTrip_tripEvents;\n        // Always ensure tasking events are available in the base events array\n        const taskingEvents = [\n            \"TaskingStartUnderway\",\n            \"TaskingOnScene\",\n            \"TaskingOnTow\",\n            \"TaskingComplete\",\n            \"TaskingPaused\",\n            \"TaskingResumed\"\n        ];\n        // Add missing tasking events to the events array\n        const eventsWithTasking = [\n            ...events\n        ];\n        taskingEvents.forEach((taskingType)=>{\n            if (!eventsWithTasking.find((event)=>event.value === taskingType)) {\n                eventsWithTasking.push({\n                    label: taskingType.replace(/([a-z])([A-Z])/g, \"$1 $2\"),\n                    value: taskingType\n                });\n            }\n        });\n        if (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes = _currentTrip_tripEvents.nodes) === null || _currentTrip_tripEvents_nodes === void 0 ? void 0 : _currentTrip_tripEvents_nodes.find((event)=>{\n            var _event_eventType_Tasking, _event_eventType_Tasking1;\n            return event.eventCategory === \"Tasking\" && ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\" && ((_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.status) === \"Open\";\n        })) {\n            var _currentTrip_tripEvents_nodes_filter, _currentTrip_tripEvents_nodes_filter1;\n            const openTask = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_filter = currentTrip.tripEvents.nodes.filter((event)=>{\n                var _event_eventType_Tasking;\n                return event.eventCategory === \"Tasking\" && ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\";\n            })) === null || _currentTrip_tripEvents_nodes_filter === void 0 ? void 0 : _currentTrip_tripEvents_nodes_filter.length;\n            const pausedTask = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_filter1 = currentTrip.tripEvents.nodes.filter((event)=>{\n                var _event_eventType_Tasking;\n                return event.eventCategory === \"Tasking\" && ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingPaused\";\n            })) === null || _currentTrip_tripEvents_nodes_filter1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_filter1.length;\n            const sortedEvents = [\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingStartUnderway\" && openTask - pausedTask < 1).map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingOnScene\").map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingOnTow\").map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingComplete\").map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingPaused\").map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingResumed\" && pausedTask > 0).map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>!event.value.includes(\"Tasking\"))\n            ];\n            return sortedEvents;\n        }\n        return eventsWithTasking;\n    };\n    /*const colourStyles: StylesConfig = {\r\n        option: (\r\n            styles: any,\r\n            {\r\n                data,\r\n                isDisabled,\r\n                isFocused,\r\n                isSelected,\r\n            }: { data: any; isDisabled: any; isFocused: any; isSelected: any },\r\n        ) => {\r\n            const color = data.color\r\n            return {\r\n                ...styles,\r\n                backgroundColor: isDisabled\r\n                    ? undefined\r\n                    : isSelected\r\n                      ? data.bgColor\r\n                      : isFocused\r\n                        ? data.bgColor\r\n                        : data.bgColor + '60',\r\n                color: data.color,\r\n            }\r\n        },\r\n        singleValue: (styles: any, data: any) => ({\r\n            ...styles,\r\n            color: events.find((option: any) => option.value == data.data.value)\r\n                ?.color,\r\n        }),\r\n    }*/ const formatTime = (time)=>time.slice(0, 5);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        var _currentTrip_tripEvents_nodes_filter, _currentTrip_tripEvents;\n        const taskingEvents = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_filter = _currentTrip_tripEvents.nodes.filter((event)=>{\n            var _event_eventType_Tasking, _event_eventType_Tasking1, _event_eventType_Tasking2;\n            return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && ((event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\" || (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.type) === \"TaskingPaused\") && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.status) === \"Open\";\n        })) === null || _currentTrip_tripEvents_nodes_filter === void 0 ? void 0 : _currentTrip_tripEvents_nodes_filter.length;\n        setTaskingEvents(taskingEvents);\n    }, [\n        currentTrip\n    ]);\n    const hasParent = (field)=>{\n        const config = _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_17__.SLALL_LogBookFields.find((localField)=>localField.componentClass === \"EventType_LogBookComponent\");\n        const hasGroup = config === null || config === void 0 ? void 0 : config.items.find((localField)=>field.fieldName === localField.value && localField.groupTo);\n        return hasGroup ? true : false;\n    };\n    const isTowingField = (field)=>{\n        const config = _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_17__.SLALL_LogBookFields.find((localField)=>localField.componentClass === \"EventType_LogBookComponent\");\n        const isTowingCategory = config === null || config === void 0 ? void 0 : config.items.find((localField)=>field === localField.value && localField.type === \"TowingSubCategory\");\n        return isTowingCategory ? true : false;\n    };\n    const handleEventChange = (event)=>{\n        setCurrentEvent(false);\n        setCurrentStop(false);\n        setTripReport_Stops(false);\n        setDisplayDangerousGoodsPvpd(false);\n        setDisplayDangerousGoodsPvpdSailing(null);\n        fetchActivityTypes();\n        setCurrentEventType(event);\n    };\n    // const handleSetOpenEventModal = () => {\n    // setOpenEventModal(!openEventModal)\n    // }\n    const handleSetCurrentEventType = ()=>{\n        setCurrentEventType(false);\n        // Reset accordion state to properly close it\n        setAccordionValue(\"\");\n        setSelectedRow(0);\n        setCurrentEvent(false);\n        setCurrentStop(false);\n    // setOpenEventModal(false)\n    };\n    const previousDropEvent = (currentEvent)=>{\n        var _currentTrip_tripEvents;\n        const previousEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" && (event === null || event === void 0 ? void 0 : event.id) !== (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id));\n        return previousEvent;\n    };\n    const mainTaskingEvent = (currentEvent)=>{\n        var _currentTrip_tripEvents;\n        const mainEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.filter((event)=>{\n            var _event_eventType_Tasking;\n            return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : event.id) !== (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\";\n        });\n        return mainEvent;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (events) {\n            var _currentTrip_tripEvents_nodes_filter, _currentTrip_tripEvents_nodes, _currentTrip_tripEvents;\n            let options = [];\n            if (taskingEvents === 0) {\n                options = events.filter((event)=>(event === null || event === void 0 ? void 0 : event.value) !== \"TaskingOnScene\" && (event === null || event === void 0 ? void 0 : event.value) !== \"TaskingOnTow\" && (event === null || event === void 0 ? void 0 : event.value) !== \"TaskingPaused\" && (event === null || event === void 0 ? void 0 : event.value) !== \"TaskingResumed\" && (event === null || event === void 0 ? void 0 : event.value) !== \"TaskingComplete\");\n                // Ensure TaskingStartUnderway is always available when no tasking events are open\n                const taskingStartUnderwayOption = events.find((event)=>(event === null || event === void 0 ? void 0 : event.value) === \"TaskingStartUnderway\");\n                if (taskingStartUnderwayOption && !options.find((option)=>option.value === \"TaskingStartUnderway\")) {\n                    options.push(taskingStartUnderwayOption);\n                } else if (!taskingStartUnderwayOption) {\n                    // If TaskingStartUnderway is not in the events array, create it manually\n                    options.push({\n                        label: \"Tasking Start Underway\",\n                        value: \"TaskingStartUnderway\"\n                    });\n                }\n            } else {\n                var _currentTrip_tripEvents1, _currentTrip_tripEvents2, _currentTrip_tripEvents3;\n                const taskingOpen = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : _currentTrip_tripEvents1.nodes.filter((event)=>{\n                    var _event_eventType_Tasking, _event_eventType_Tasking1;\n                    return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.status) === \"Open\";\n                });\n                const taskingPaused = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents2 = currentTrip.tripEvents) === null || _currentTrip_tripEvents2 === void 0 ? void 0 : _currentTrip_tripEvents2.nodes.filter((event)=>{\n                    var _event_eventType_Tasking;\n                    return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingPaused\";\n                });\n                const taskingResumed = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents3 = currentTrip.tripEvents) === null || _currentTrip_tripEvents3 === void 0 ? void 0 : _currentTrip_tripEvents3.nodes.filter((event)=>{\n                    var _event_eventType_Tasking;\n                    return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingResumed\";\n                });\n                if ((taskingOpen === null || taskingOpen === void 0 ? void 0 : taskingOpen.length) > 0) {\n                    if ((taskingPaused === null || taskingPaused === void 0 ? void 0 : taskingPaused.length) === (taskingResumed === null || taskingResumed === void 0 ? void 0 : taskingResumed.length)) {\n                        options = events.filter((event)=>(event === null || event === void 0 ? void 0 : event.value) !== \"TaskingResumed\");\n                    }\n                    if ((taskingPaused === null || taskingPaused === void 0 ? void 0 : taskingPaused.length) > (taskingResumed === null || taskingResumed === void 0 ? void 0 : taskingResumed.length)) {\n                        options = events.filter((event)=>event.value !== \"TaskingOnScene\" && event.value !== \"TaskingOnTow\" && event.value !== \"TaskingPaused\");\n                    }\n                } else {\n                    options = events.filter((event)=>event.value !== \"TaskingOnScene\" && event.value !== \"TaskingOnTow\" && event.value !== \"TaskingPaused\" && event.value !== \"TaskingResumed\" && event.value !== \"TaskingComplete\");\n                    // Ensure TaskingStartUnderway is available when no open tasking events exist\n                    const taskingStartUnderwayOption = events.find((event)=>(event === null || event === void 0 ? void 0 : event.value) === \"TaskingStartUnderway\");\n                    if (taskingStartUnderwayOption && !options.find((option)=>option.value === \"TaskingStartUnderway\")) {\n                        options.push(taskingStartUnderwayOption);\n                    } else if (!taskingStartUnderwayOption) {\n                        // If TaskingStartUnderway is not in the events array, create it manually\n                        options.push({\n                            label: \"Tasking Start Underway\",\n                            value: \"TaskingStartUnderway\"\n                        });\n                    }\n                }\n            }\n            // When taskingPaused > 0, ensure TaskingResumed and TaskingStartUnderway are available\n            const taskingPausedCount = (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes = _currentTrip_tripEvents.nodes) === null || _currentTrip_tripEvents_nodes === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_filter = _currentTrip_tripEvents_nodes.filter((event)=>{\n                var _event_eventType_Tasking;\n                return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingPaused\";\n            })) === null || _currentTrip_tripEvents_nodes_filter === void 0 ? void 0 : _currentTrip_tripEvents_nodes_filter.length) || 0;\n            if (taskingPausedCount > 0) {\n                // Find TaskingResumed and TaskingStartUnderway from the original events array\n                const taskingResumedOption = events.find((event)=>(event === null || event === void 0 ? void 0 : event.value) === \"TaskingResumed\");\n                const taskingStartUnderwayOption = events.find((event)=>(event === null || event === void 0 ? void 0 : event.value) === \"TaskingStartUnderway\");\n                // Add TaskingResumed if it exists in events but not in current options\n                if (taskingResumedOption && !options.find((option)=>option.value === \"TaskingResumed\")) {\n                    options.push(taskingResumedOption);\n                }\n                // Add TaskingStartUnderway if it exists in events but not in current options\n                if (taskingStartUnderwayOption && !options.find((option)=>option.value === \"TaskingStartUnderway\")) {\n                    options.push(taskingStartUnderwayOption);\n                } else if (!taskingStartUnderwayOption) {\n                    // If TaskingStartUnderway is not in the events array, create it manually\n                    options.push({\n                        label: \"Tasking Start Underway\",\n                        value: \"TaskingStartUnderway\"\n                    });\n                }\n            }\n            options = options.map((option)=>{\n                if (option.value.includes(\"Tasking\") && option.value !== \"TaskingStartUnderway\" && !option.className) {\n                    return {\n                        ...option,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    };\n                }\n                return option;\n            });\n            // Remove duplicate by checking the options.value\n            options = options.filter((option, index, self)=>index === self.findIndex((o)=>o.value === option.value));\n            // Remove InfringementNotices from options because there's already InfringementNotice (without an 's').\n            options = options.filter((option)=>option.value !== \"InfringementNotices\");\n            // Remove HumanRescue and VesselRescue from options since it's already included in Tasking\n            options = options.filter((option)=>option.value !== \"HumanRescue\" && option.value !== \"VesselRescue\");\n            setActivityTypeOptions(options);\n        }\n    }, [\n        events,\n        currentTrip,\n        taskingEvents\n    ]);\n    const fetchActivityTypes = ()=>{\n        initData();\n    };\n    // Memoized function to handle stop accordion item clicks\n    const handleStopAccordionItemClick = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((eventId)=>{\n        return ()=>{\n            // Toggle accordion state\n            if (accordionValue === \"stop_\".concat(eventId)) {\n                setAccordionValue(\"\");\n                setSelectedRow(0);\n                setCurrentEventType([]);\n                setCurrentEvent(false);\n                setCurrentStop(false);\n            } else {\n                var _currentTrip_tripReport_Stops;\n                setAccordionValue(\"stop_\".concat(eventId));\n                setSelectedRow(eventId);\n                setCurrentEventType({\n                    label: \"Passenger/vehicle pickup/drop off\",\n                    value: \"PassengerVehiclePickDrop\"\n                });\n                // Find the event by ID\n                const event = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops === void 0 ? void 0 : _currentTrip_tripReport_Stops.nodes.find((stop)=>stop.id === eventId);\n                setCurrentStop(event);\n                setDisplayDangerousGoodsPvpd(false);\n                setDisplayDangerousGoodsPvpdSailing(null);\n                setTripReport_Stops(false);\n            }\n        };\n    }, [\n        accordionValue,\n        currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops === void 0 ? void 0 : _currentTrip_tripReport_Stops.nodes,\n        setSelectedRow,\n        setCurrentEventType,\n        setCurrentEvent,\n        setCurrentStop,\n        setDisplayDangerousGoodsPvpd,\n        setDisplayDangerousGoodsPvpdSailing,\n        setTripReport_Stops\n    ]);\n    // Memoized function to generate stop display text\n    const getStopDisplayText = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((event)=>{\n        var _event_stopLocation, _event_stopLocation1;\n        return \"Passenger / Vehicle Pick & Drop - \".concat((event === null || event === void 0 ? void 0 : event.arriveTime) ? (event === null || event === void 0 ? void 0 : event.arriveTime) + \" (arr)\" : \"\", \" \").concat((event === null || event === void 0 ? void 0 : event.arriveTime) && (event === null || event === void 0 ? void 0 : event.departTime) ? \"-\" : \"\", \" \").concat((event === null || event === void 0 ? void 0 : event.departTime) ? (event === null || event === void 0 ? void 0 : event.departTime) + \" (dep)\" : \"\", \" \").concat((event === null || event === void 0 ? void 0 : (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.title) ? event === null || event === void 0 ? void 0 : (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.title : \"\");\n    }, []);\n    const shouldIndent = (event)=>{\n        var _event_eventType_Tasking;\n        return event.eventCategory === \"Tasking\" && ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) !== \"TaskingStartUnderway\";\n    };\n    const getEventLabel = (event)=>{\n        var _event_eventType_PassengerDropFacility_type_replace_replace, _event_eventType_PassengerDropFacility_type_replace, _event_eventType_PassengerDropFacility_type, _event_eventType_PassengerDropFacility, _event_eventType_Tasking;\n        return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" ? event === null || event === void 0 ? void 0 : (_event_eventType_PassengerDropFacility = event.eventType_PassengerDropFacility) === null || _event_eventType_PassengerDropFacility === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type = _event_eventType_PassengerDropFacility.type) === null || _event_eventType_PassengerDropFacility_type === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type_replace = _event_eventType_PassengerDropFacility_type.replace(/([a-z])([A-Z])/g, \"$1 $2\")) === null || _event_eventType_PassengerDropFacility_type_replace === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type_replace_replace = _event_eventType_PassengerDropFacility_type_replace.replace(\"Passenger Arrival\", \"Arrival\")) === null || _event_eventType_PassengerDropFacility_type_replace_replace === void 0 ? void 0 : _event_eventType_PassengerDropFacility_type_replace_replace.replace(\"Passenger Departure\", \"Departure\") : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" ? event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type.replace(/([a-z])([A-Z])/g, \"$1 $2\") : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"EventSupernumerary\" ? \"Supernumerary\" : event === null || event === void 0 ? void 0 : event.eventCategory.replace(/([a-z])([A-Z])/g, \"$1 $2\");\n    };\n    const getEventValue = (event)=>{\n        var _event_eventType_PassengerDropFacility, _event_eventType_Tasking;\n        return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" ? event === null || event === void 0 ? void 0 : (_event_eventType_PassengerDropFacility = event.eventType_PassengerDropFacility) === null || _event_eventType_PassengerDropFacility === void 0 ? void 0 : _event_eventType_PassengerDropFacility.type : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" ? event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Supernumerary\" ? \"EventSupernumerary\" : event === null || event === void 0 ? void 0 : event.eventCategory;\n    };\n    const getFuelTotals = (fuelLogs)=>{\n        const totalFuel = fuelLogs.reduce((acc, log)=>{\n            return acc + (log === null || log === void 0 ? void 0 : log.fuelAdded);\n        }, 0);\n        const totalCost = fuelLogs.reduce((acc, log)=>{\n            return acc + (log === null || log === void 0 ? void 0 : log.fuelAdded) * (log === null || log === void 0 ? void 0 : log.costPerLitre);\n        }, 0);\n        return \" - Total Fuel Added: \" + totalFuel + \"L, Total Cost: $\" + totalCost;\n    };\n    const getEventDisplayText = (event)=>{\n        var _eventType_geoLocation;\n        const category = event.eventCategory;\n        const eventType = event[\"eventType_\".concat(category)];\n        const geoLocation = eventType === null || eventType === void 0 ? void 0 : (_eventType_geoLocation = eventType.geoLocation) === null || _eventType_geoLocation === void 0 ? void 0 : _eventType_geoLocation.title;\n        const title = eventType === null || eventType === void 0 ? void 0 : eventType.title;\n        switch(category){\n            case \"PassengerDropFacility\":\n                var _eventType_type_replace_replace, _eventType_type_replace, _eventType_type;\n                return ((eventType === null || eventType === void 0 ? void 0 : eventType.time) ? eventType.time + \" - \" : \"\") + (eventType === null || eventType === void 0 ? void 0 : (_eventType_type = eventType.type) === null || _eventType_type === void 0 ? void 0 : (_eventType_type_replace = _eventType_type.replace(/([a-z])([A-Z])/g, \"$1 $2\")) === null || _eventType_type_replace === void 0 ? void 0 : (_eventType_type_replace_replace = _eventType_type_replace.replace(\"Passenger Arrival\", \"Arrival\")) === null || _eventType_type_replace_replace === void 0 ? void 0 : _eventType_type_replace_replace.replace(\"Passenger Departure\", \"Departure\")) + (title ? \" - \" + title : \"\") + (geoLocation ? \" - \" + geoLocation : \"\");\n            case \"Tasking\":\n                var _eventType_type1;\n                return (eventType === null || eventType === void 0 ? void 0 : eventType.time) + \" - \" + (eventType === null || eventType === void 0 ? void 0 : (_eventType_type1 = eventType.type) === null || _eventType_type1 === void 0 ? void 0 : _eventType_type1.replace(/([a-z])([A-Z])/g, \"$1 $2\")) + (title ? \" - \" + title : \"\") + (geoLocation ? \" - \" + geoLocation : \"\");\n            case \"BarCrossing\":\n                return ((eventType === null || eventType === void 0 ? void 0 : eventType.time) ? eventType.time + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (geoLocation ? \" - \" + geoLocation : \"\");\n            case \"RefuellingBunkering\":\n                return ((eventType === null || eventType === void 0 ? void 0 : eventType.date) ? dayjs__WEBPACK_IMPORTED_MODULE_21___default()(eventType.date).format(\"HH:mm\") + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (geoLocation ? \" - \" + geoLocation : \"\");\n            case \"RestrictedVisibility\":\n                var _eventType_startLocation;\n                return ((eventType === null || eventType === void 0 ? void 0 : eventType.crossingTime) ? eventType.crossingTime + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + ((eventType === null || eventType === void 0 ? void 0 : (_eventType_startLocation = eventType.startLocation) === null || _eventType_startLocation === void 0 ? void 0 : _eventType_startLocation.title) ? \" - \" + eventType.startLocation.title : \"\");\n            case \"TripUpdate\":\n                var _event_tripUpdate, _event_tripUpdate_geoLocation, _event_tripUpdate1, _event_tripUpdate_geoLocation1, _event_tripUpdate2;\n                return (((_event_tripUpdate = event.tripUpdate) === null || _event_tripUpdate === void 0 ? void 0 : _event_tripUpdate.date) ? dayjs__WEBPACK_IMPORTED_MODULE_21___default()(event.tripUpdate.date).format(\"HH:mm\") + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (((_event_tripUpdate1 = event.tripUpdate) === null || _event_tripUpdate1 === void 0 ? void 0 : (_event_tripUpdate_geoLocation = _event_tripUpdate1.geoLocation) === null || _event_tripUpdate_geoLocation === void 0 ? void 0 : _event_tripUpdate_geoLocation.title) ? \" - \" + ((_event_tripUpdate2 = event.tripUpdate) === null || _event_tripUpdate2 === void 0 ? void 0 : (_event_tripUpdate_geoLocation1 = _event_tripUpdate2.geoLocation) === null || _event_tripUpdate_geoLocation1 === void 0 ? void 0 : _event_tripUpdate_geoLocation1.title) : \"\");\n            case \"EventSupernumerary\":\n                var _event_supernumerary, _event_supernumerary1, _event_supernumerary2;\n                return ((event === null || event === void 0 ? void 0 : (_event_supernumerary = event.supernumerary) === null || _event_supernumerary === void 0 ? void 0 : _event_supernumerary.briefingTime) ? (event === null || event === void 0 ? void 0 : (_event_supernumerary1 = event.supernumerary) === null || _event_supernumerary1 === void 0 ? void 0 : _event_supernumerary1.briefingTime) + \" - \" : \"\") + \"Supernumerary\" + ((event === null || event === void 0 ? void 0 : (_event_supernumerary2 = event.supernumerary) === null || _event_supernumerary2 === void 0 ? void 0 : _event_supernumerary2.title) ? \" - \" + (event === null || event === void 0 ? void 0 : event.supernumerary.title) : \"\");\n            case \"IncidentRecord\":\n                var _event_incidentRecord, _event_incidentRecord1;\n                return (((_event_incidentRecord = event.incidentRecord) === null || _event_incidentRecord === void 0 ? void 0 : _event_incidentRecord.startDate) ? dayjs__WEBPACK_IMPORTED_MODULE_21___default()(event.incidentRecord.startDate).format(\"HH:mm\") + \" - \" : \"\") + \"Incident Record\" + (((_event_incidentRecord1 = event.incidentRecord) === null || _event_incidentRecord1 === void 0 ? void 0 : _event_incidentRecord1.title) ? \" - \" + event.incidentRecord.title : \"\");\n            case \"InfringementNotice\":\n                var _event_infringementNotice, _event_infringementNotice1, _event_infringementNotice2;\n                return ((event === null || event === void 0 ? void 0 : (_event_infringementNotice = event.infringementNotice) === null || _event_infringementNotice === void 0 ? void 0 : _event_infringementNotice.time) ? dayjs__WEBPACK_IMPORTED_MODULE_21___default()(\"\".concat(dayjs__WEBPACK_IMPORTED_MODULE_21___default()().format(\"YYYY-MM-DD\"), \" \").concat(event === null || event === void 0 ? void 0 : event.infringementNotice.time)).format(\"HH:mm\") + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + ((event === null || event === void 0 ? void 0 : (_event_infringementNotice1 = event.infringementNotice) === null || _event_infringementNotice1 === void 0 ? void 0 : _event_infringementNotice1.geoLocation.title) ? \" - \" + (event === null || event === void 0 ? void 0 : (_event_infringementNotice2 = event.infringementNotice) === null || _event_infringementNotice2 === void 0 ? void 0 : _event_infringementNotice2.geoLocation.title) : \"\");\n            case \"CrewTraining\":\n                var _event_crewTraining, _event_crewTraining_geoLocation, _event_crewTraining1, _event_crewTraining_geoLocation1, _event_crewTraining2;\n                return (((_event_crewTraining = event.crewTraining) === null || _event_crewTraining === void 0 ? void 0 : _event_crewTraining.startTime) ? event.crewTraining.startTime + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (((_event_crewTraining1 = event.crewTraining) === null || _event_crewTraining1 === void 0 ? void 0 : (_event_crewTraining_geoLocation = _event_crewTraining1.geoLocation) === null || _event_crewTraining_geoLocation === void 0 ? void 0 : _event_crewTraining_geoLocation.title) ? \" - \" + ((_event_crewTraining2 = event.crewTraining) === null || _event_crewTraining2 === void 0 ? void 0 : (_event_crewTraining_geoLocation1 = _event_crewTraining2.geoLocation) === null || _event_crewTraining_geoLocation1 === void 0 ? void 0 : _event_crewTraining_geoLocation1.title) : \"\");\n            case \"VesselRescue\":\n                var _event_eventType_VesselRescue_mission, _event_eventType_VesselRescue, _event_eventType_VesselRescue_mission1, _event_eventType_VesselRescue1, _event_eventType_VesselRescue2;\n                return (((_event_eventType_VesselRescue = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue === void 0 ? void 0 : (_event_eventType_VesselRescue_mission = _event_eventType_VesselRescue.mission) === null || _event_eventType_VesselRescue_mission === void 0 ? void 0 : _event_eventType_VesselRescue_mission.completedAt) ? ((_event_eventType_VesselRescue1 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue1 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission1 = _event_eventType_VesselRescue1.mission) === null || _event_eventType_VesselRescue_mission1 === void 0 ? void 0 : _event_eventType_VesselRescue_mission1.completedAt) + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (((_event_eventType_VesselRescue2 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue2 === void 0 ? void 0 : _event_eventType_VesselRescue2.vesselName) ? \" - \" + event.eventType_VesselRescue.vesselName : \"\");\n            case \"HumanRescue\":\n                var _event_eventType_PersonRescue_mission, _event_eventType_PersonRescue, _event_eventType_PersonRescue_mission1, _event_eventType_PersonRescue1, _event_eventType_PersonRescue2;\n                return (((_event_eventType_PersonRescue = event.eventType_PersonRescue) === null || _event_eventType_PersonRescue === void 0 ? void 0 : (_event_eventType_PersonRescue_mission = _event_eventType_PersonRescue.mission) === null || _event_eventType_PersonRescue_mission === void 0 ? void 0 : _event_eventType_PersonRescue_mission.completedAt) ? ((_event_eventType_PersonRescue1 = event.eventType_PersonRescue) === null || _event_eventType_PersonRescue1 === void 0 ? void 0 : (_event_eventType_PersonRescue_mission1 = _event_eventType_PersonRescue1.mission) === null || _event_eventType_PersonRescue_mission1 === void 0 ? void 0 : _event_eventType_PersonRescue_mission1.completedAt) + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (((_event_eventType_PersonRescue2 = event.eventType_PersonRescue) === null || _event_eventType_PersonRescue2 === void 0 ? void 0 : _event_eventType_PersonRescue2.personName) ? \" - \" + event.eventType_PersonRescue.personName : \"\");\n            default:\n                return category.replace(/([a-z])([A-Z])/g, \"$1 $2\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row justify-between items-end\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_25__.H4, {\n                            children: \"ACTIVITIES\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 867,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_25__.P, {\n                            children: \"Record the events that happen during a voyage in this section.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 868,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                    lineNumber: 866,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                lineNumber: 865,\n                columnNumber: 13\n            }, this),\n            (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes = _currentTrip_tripEvents.nodes) === null || _currentTrip_tripEvents_nodes === void 0 ? void 0 : _currentTrip_tripEvents_nodes.length) > 0 || (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops1 = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops1 === void 0 ? void 0 : (_currentTrip_tripReport_Stops_nodes = _currentTrip_tripReport_Stops1.nodes) === null || _currentTrip_tripReport_Stops_nodes === void 0 ? void 0 : _currentTrip_tripReport_Stops_nodes.length) > 0 || !currentEvent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: ((currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes1 = _currentTrip_tripEvents1.nodes) === null || _currentTrip_tripEvents_nodes1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes1.length) > 0 || (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops2 = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops2 === void 0 ? void 0 : (_currentTrip_tripReport_Stops_nodes1 = _currentTrip_tripReport_Stops2.nodes) === null || _currentTrip_tripReport_Stops_nodes1 === void 0 ? void 0 : _currentTrip_tripReport_Stops_nodes1.length) > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.Accordion, {\n                    type: \"single\",\n                    collapsible: true,\n                    value: accordionValue,\n                    onValueChange: (value)=>{\n                        setAccordionValue(value);\n                        // If we're closing the accordion, reset the state\n                        if (value === \"\") {\n                            setSelectedRow(0);\n                            // setOpenEventModal(false)\n                            setCurrentEventType([]);\n                            setCurrentEvent(false);\n                            setCurrentStop(false);\n                        }\n                    },\n                    children: [\n                        currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.tripEvents.nodes.map((event, index)=>{\n                            var _event_eventType_PassengerDropFacility_type_replace_replace, _event_eventType_PassengerDropFacility_type_replace, _event_eventType_PassengerDropFacility_type, _event_eventType_PassengerDropFacility, _event_eventType_Tasking, _event_eventType_PassengerDropFacility1, _event_eventType_Tasking1, _event_eventType_Tasking2, _event_eventType_Tasking3, _event_eventType_RefuellingBunkering_fuelLog_nodes, _event_eventType_RefuellingBunkering_fuelLog, _event_eventType_RefuellingBunkering, _event_eventType_RefuellingBunkering_fuelLog1, _event_eventType_RefuellingBunkering1, _event_eventType_Tasking4, _event_eventType_Tasking5, _event_eventType_Tasking6, _event_eventType_Tasking7;\n                            // Generate event label and value outside the JSX\n                            const eventLabel = (event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" ? event === null || event === void 0 ? void 0 : (_event_eventType_PassengerDropFacility = event.eventType_PassengerDropFacility) === null || _event_eventType_PassengerDropFacility === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type = _event_eventType_PassengerDropFacility.type) === null || _event_eventType_PassengerDropFacility_type === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type_replace = _event_eventType_PassengerDropFacility_type.replace(/([a-z])([A-Z])/g, \"$1 $2\")) === null || _event_eventType_PassengerDropFacility_type_replace === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type_replace_replace = _event_eventType_PassengerDropFacility_type_replace.replace(\"Passenger Arrival\", \"Arrival\")) === null || _event_eventType_PassengerDropFacility_type_replace_replace === void 0 ? void 0 : _event_eventType_PassengerDropFacility_type_replace_replace.replace(\"Passenger Departure\", \"Departure\") : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" ? event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type.replace(/([a-z])([A-Z])/g, \"$1 $2\") : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"EventSupernumerary\" ? \"Supernumerary\" : event === null || event === void 0 ? void 0 : event.eventCategory.replace(/([a-z])([A-Z])/g, \"$1 $2\");\n                            const eventValue = (event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" ? event === null || event === void 0 ? void 0 : (_event_eventType_PassengerDropFacility1 = event.eventType_PassengerDropFacility) === null || _event_eventType_PassengerDropFacility1 === void 0 ? void 0 : _event_eventType_PassengerDropFacility1.type : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" ? event === null || event === void 0 ? void 0 : (_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.type : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Supernumerary\" ? \"EventSupernumerary\" : event === null || event === void 0 ? void 0 : event.eventCategory;\n                            // Generate event display text\n                            const eventDisplayText = getEventDisplayText(event);\n                            // Handle click on accordion item\n                            const handleAccordionItemClick = ()=>{\n                                // Toggle accordion state\n                                if (accordionValue === event.id.toString()) {\n                                    setAccordionValue(\"\");\n                                    setSelectedRow(0);\n                                    // setOpenEventModal(false)\n                                    setCurrentEventType([]);\n                                    setCurrentEvent(false);\n                                    setCurrentStop(false);\n                                } else {\n                                    setAccordionValue(event.id.toString());\n                                    setSelectedRow(event.id);\n                                    // setOpenEventModal(true)\n                                    setCurrentEventType({\n                                        label: eventLabel,\n                                        value: eventValue\n                                    });\n                                    setCurrentEvent(event);\n                                    setTripReport_Stops(false);\n                                    setDisplayDangerousGoodsPvpd(false);\n                                    setDisplayDangerousGoodsPvpdSailing(null);\n                                }\n                            };\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionItem, {\n                                value: event.id.toString(),\n                                className: (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.type) !== \"TaskingStartUnderway\" ? \"ml-[1.5rem]\" : \"\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionTrigger, {\n                                        onClick: handleAccordionItemClick,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center relative justify-between w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col inset-y-0 items-center justify-center absolute -left-[41px] sm:-left-[46px] w-5\",\n                                                    children: ((event === null || event === void 0 ? void 0 : event.eventCategory) !== \"Tasking\" || (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking3 = event.eventType_Tasking) === null || _event_eventType_Tasking3 === void 0 ? void 0 : _event_eventType_Tasking3.type) == \"TaskingStartUnderway\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_29__.cn)(\"size-[11px] z-10 rounded-full\", currentEvent.id === event.id ? \"border border-primary bg-curious-blue-200\" : currentEvent.eventCategory === event.eventCategory ? \"border border-primary bg-curious-blue-200\" : \"border border-cool-wedgewood-200 bg-outer-space-50\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                        lineNumber: 1003,\n                                                        columnNumber: 61\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 996,\n                                                    columnNumber: 53\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        eventDisplayText,\n                                                        (event === null || event === void 0 ? void 0 : event.eventCategory) === \"RefuellingBunkering\" && (event === null || event === void 0 ? void 0 : (_event_eventType_RefuellingBunkering = event.eventType_RefuellingBunkering) === null || _event_eventType_RefuellingBunkering === void 0 ? void 0 : (_event_eventType_RefuellingBunkering_fuelLog = _event_eventType_RefuellingBunkering.fuelLog) === null || _event_eventType_RefuellingBunkering_fuelLog === void 0 ? void 0 : (_event_eventType_RefuellingBunkering_fuelLog_nodes = _event_eventType_RefuellingBunkering_fuelLog.nodes) === null || _event_eventType_RefuellingBunkering_fuelLog_nodes === void 0 ? void 0 : _event_eventType_RefuellingBunkering_fuelLog_nodes.length) > 0 && getFuelTotals(event === null || event === void 0 ? void 0 : (_event_eventType_RefuellingBunkering1 = event.eventType_RefuellingBunkering) === null || _event_eventType_RefuellingBunkering1 === void 0 ? void 0 : (_event_eventType_RefuellingBunkering_fuelLog1 = _event_eventType_RefuellingBunkering1.fuelLog) === null || _event_eventType_RefuellingBunkering_fuelLog1 === void 0 ? void 0 : _event_eventType_RefuellingBunkering_fuelLog1.nodes)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1017,\n                                                    columnNumber: 53\n                                                }, this),\n                                                (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking4 = event.eventType_Tasking) === null || _event_eventType_Tasking4 === void 0 ? void 0 : _event_eventType_Tasking4.type) === \"TaskingStartUnderway\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking5 = event.eventType_Tasking) === null || _event_eventType_Tasking5 === void 0 ? void 0 : _event_eventType_Tasking5.status) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"\".concat((event === null || event === void 0 ? void 0 : (_event_eventType_Tasking6 = event.eventType_Tasking) === null || _event_eventType_Tasking6 === void 0 ? void 0 : _event_eventType_Tasking6.status) === \"Open\" ? \"text-bright-turquoise-600\" : \"\", \" pr-2\"),\n                                                    children: event === null || event === void 0 ? void 0 : (_event_eventType_Tasking7 = event.eventType_Tasking) === null || _event_eventType_Tasking7 === void 0 ? void 0 : _event_eventType_Tasking7.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1039,\n                                                    columnNumber: 61\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                            lineNumber: 995,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                        lineNumber: 991,\n                                        columnNumber: 45\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionContent, {\n                                        className: \"pb-4\",\n                                        children: currentEventType && currentEvent && currentEvent.id === event.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                currentEventType.value === \"VesselRescue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_vessel_rescue__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    offline: offline,\n                                                    geoLocations: geoLocations,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1058,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"HumanRescue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_person_rescue__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    offline: offline,\n                                                    geoLocations: geoLocations,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1088,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"RestrictedVisibility\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_restricted_visibility__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    logBookConfig: logBookConfig,\n                                                    locked: locked || !edit_tripActivity,\n                                                    members: crewMembers\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1118,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"BarCrossing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_bar_crossing__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    members: crewMembers,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    logBookConfig: logBookConfig,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1151,\n                                                    columnNumber: 65\n                                                }, this),\n                                                (currentEventType.value === \"PassengerArrival\" || currentEventType.value === \"PassengerDeparture\" || currentEventType.value === \"WaterTaxiService\" || currentEventType.value === \"ScheduledPassengerService\") && //TODO: update this form\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_passenger_drop_facility__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    offline: offline,\n                                                    geoLocations: geoLocations,\n                                                    inLogbook: true,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    type: currentEventType.value,\n                                                    logBookConfig: logBookConfig,\n                                                    previousDropEvent: previousDropEvent(currentEvent),\n                                                    vessel: vessel,\n                                                    locked: locked || !edit_tripActivity,\n                                                    fuelLogs: fuelLogs\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1191,\n                                                    columnNumber: 65\n                                                }, this),\n                                                (currentEventType.value === \"TaskingStartUnderway\" || currentEventType.value === \"TaskingOnScene\" || currentEventType.value === \"TaskingOnTow\" || currentEventType.value === \"TaskingPaused\" || currentEventType.value === \"TaskingResumed\" || currentEventType.value === \"TaskingComplete\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_tasking__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    offline: offline,\n                                                    geoLocations: geoLocations,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    type: currentEventType.value,\n                                                    logBookConfig: logBookConfig,\n                                                    previousDropEvent: mainTaskingEvent(currentEvent),\n                                                    vessel: vessel,\n                                                    members: crewMembers,\n                                                    locked: locked || !edit_tripActivity,\n                                                    fuelLogs: fuelLogs\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1247,\n                                                    columnNumber: 65\n                                                }, this),\n                                                permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__.hasPermission)(\"RECORD_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: currentEventType.value === \"CrewTraining\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_crew_training_event__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        offline: offline,\n                                                        vesselId: +vesselID,\n                                                        trainingTypeId: 0,\n                                                        currentTrip: currentTrip,\n                                                        updateTripReport: updateTripReport,\n                                                        selectedEvent: currentEvent,\n                                                        tripReport: tripReport,\n                                                        closeModal: handleSetCurrentEventType,\n                                                        crewMembers: crewMembers,\n                                                        masterID: masterID,\n                                                        logBookConfig: logBookConfig,\n                                                        vessels: vessels,\n                                                        locked: locked || !edit_tripActivity,\n                                                        logBookStartDate: logBookStartDate\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                        lineNumber: 1301,\n                                                        columnNumber: 77\n                                                    }, this)\n                                                }, void 0, false),\n                                                currentEventType.value === \"EventSupernumerary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_supernumerary_event__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    offline: offline,\n                                                    logBookConfig: logBookConfig,\n                                                    locked: locked || !edit_tripActivity,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    tripReport: tripReport,\n                                                    selectedEvent: currentEvent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1351,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"RefuellingBunkering\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_refuelling_bunkering__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    logBookConfig: logBookConfig,\n                                                    locked: locked || !edit_tripActivity,\n                                                    mainFuelLogs: fuelLogs\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1381,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"TripUpdate\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_trip_update__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1417,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"PilotTransfer\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_pilot_transfer__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1444,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"InfringementNotice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_InfringementNotices__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    crewMembers: crewMembers,\n                                                    locked: locked || !edit_tripActivity,\n                                                    visibility: // selectedRow ===\n                                                    //     event.id &&\n                                                    currentEventType && currentEvent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1471,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"IncidentRecord\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_incident_record_incident_record_form__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    id: (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.incidentRecordID) || 0,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    inLogbook: true,\n                                                    selectedEvent: currentEvent,\n                                                    offline: offline,\n                                                    tripReport: tripReport\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1507,\n                                                    columnNumber: 65\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                        lineNumber: 1050,\n                                        columnNumber: 45\n                                    }, this)\n                                ]\n                            }, index + \"_events\", true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                lineNumber: 979,\n                                columnNumber: 41\n                            }, this);\n                        }),\n                        currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops3 = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops3 === void 0 ? void 0 : _currentTrip_tripReport_Stops3.nodes.map((event, index)=>{\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionItem, {\n                                value: \"stop_\".concat(event.id),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionTrigger, {\n                                        onClick: handleStopAccordionItemClick(event.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: getStopDisplayText(event)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                lineNumber: 1555,\n                                                columnNumber: 53\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                            lineNumber: 1554,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                        lineNumber: 1550,\n                                        columnNumber: 45\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionContent, {\n                                        className: \"pb-4\",\n                                        children: currentEventType && currentStop && currentStop.id === event.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: currentEventType.value === \"PassengerVehiclePickDrop\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_passenger_vehicle_pick_drop__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                offline: offline,\n                                                geoLocations: geoLocations,\n                                                updateTripReport: updateTripReport,\n                                                currentTrip: currentTrip,\n                                                selectedEvent: currentStop,\n                                                tripReport: tripReport,\n                                                closeModal: handleSetCurrentEventType,\n                                                type: currentEventType.value,\n                                                logBookConfig: logBookConfig,\n                                                members: crewMembers,\n                                                locked: locked || !edit_tripActivity,\n                                                tripReport_Stops: tripReport_Stops,\n                                                setTripReport_Stops: setTripReport_Stops,\n                                                displayDangerousGoods: displayDangerousGoodsPvpd,\n                                                setDisplayDangerousGoods: setDisplayDangerousGoodsPvpd,\n                                                displayDangerousGoodsSailing: displayDangerousGoodsPvpdSailing,\n                                                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsPvpdSailing,\n                                                allPVPDDangerousGoods: allPVPDDangerousGoods,\n                                                setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                                                selectedDGR: selectedDGRPVPD,\n                                                setSelectedDGR: setSelectedDGRPVPD\n                                            }, \"pvpd-\".concat(event.id), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                lineNumber: 1570,\n                                                columnNumber: 65\n                                            }, this)\n                                        }, void 0, false)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                        lineNumber: 1562,\n                                        columnNumber: 45\n                                    }, this)\n                                ]\n                            }, index + \"_stops\", true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                lineNumber: 1547,\n                                columnNumber: 41\n                            }, this);\n                        })\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                    lineNumber: 881,\n                    columnNumber: 25\n                }, this)\n            }, void 0, false) : null,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-start gap-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                    position: getResponsiveLabel(bp.phablet, \"top\", \"left\"),\n                    className: \"w-full\",\n                    label: \"Activity Type \",\n                    children: activityTypeOptions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_22__.Combobox, {\n                        id: \"task-assigned\",\n                        options: activityTypeOptions,\n                        value: currentEventType,\n                        onChange: handleEventChange,\n                        title: \"Activity Type\",\n                        placeholder: \"Activity Type\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                        lineNumber: 1658,\n                        columnNumber: 25\n                    }, this) : // Failsafe - in case the activity types are not loaded.\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"outline\",\n                            onClick: fetchActivityTypes,\n                            children: \"Refresh activity types\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1669,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                        lineNumber: 1668,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                    lineNumber: 1651,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                lineNumber: 1649,\n                columnNumber: 13\n            }, this),\n            currentEventType && !currentEvent && !currentStop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    currentEventType.value === \"VesselRescue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_vessel_rescue__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            locked: locked || !edit_tripActivity\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1703,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"HumanRescue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_person_rescue__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            locked: locked || !edit_tripActivity\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1717,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"RestrictedVisibility\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_restricted_visibility__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_tripActivity,\n                            members: crewMembers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1731,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"BarCrossing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_bar_crossing__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            members: crewMembers,\n                            closeModal: handleSetCurrentEventType,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_tripActivity\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1746,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    (currentEventType.value === \"PassengerArrival\" || currentEventType.value === \"PassengerDeparture\" || currentEventType.value === \"WaterTaxiService\" || currentEventType.value === \"ScheduledPassengerService\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_passenger_drop_facility__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            type: currentEventType.value,\n                            logBookConfig: logBookConfig,\n                            inLogbook: true,\n                            previousDropEvent: previousDropEvent(currentEvent),\n                            vessel: vessel,\n                            locked: locked || !edit_tripActivity,\n                            fuelLogs: fuelLogs\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1765,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    (currentEventType.value === \"TaskingStartUnderway\" || currentEventType.value === \"TaskingOnScene\" || currentEventType.value === \"TaskingOnTow\" || currentEventType.value === \"TaskingPaused\" || currentEventType.value === \"TaskingResumed\" || currentEventType.value === \"TaskingComplete\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_tasking__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            inLogbook: true,\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            type: currentEventType.value,\n                            logBookConfig: logBookConfig,\n                            previousDropEvent: mainTaskingEvent(currentEvent),\n                            vessel: vessel,\n                            members: crewMembers,\n                            locked: locked || !edit_tripActivity,\n                            fuelLogs: fuelLogs\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1792,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__.hasPermission)(\"RECORD_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: currentEventType.value === \"CrewTraining\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_crew_training_event__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                offline: offline,\n                                vesselId: +vesselID,\n                                trainingTypeId: 0,\n                                currentTrip: currentTrip,\n                                updateTripReport: updateTripReport,\n                                selectedEvent: currentEvent,\n                                tripReport: tripReport,\n                                closeModal: handleSetCurrentEventType,\n                                crewMembers: crewMembers,\n                                masterID: masterID,\n                                logBookConfig: logBookConfig,\n                                vessels: vessels,\n                                locked: locked || !edit_tripActivity,\n                                logBookStartDate: logBookStartDate\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                lineNumber: 1818,\n                                columnNumber: 41\n                            }, this)\n                        }, void 0, false)\n                    }, void 0, false),\n                    currentEventType.value === \"EventSupernumerary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_supernumerary_event__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            inLogbook: true,\n                            offline: offline,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_tripActivity,\n                            closeModal: handleSetCurrentEventType,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            tripReport: tripReport,\n                            selectedEvent: currentEvent\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1844,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"PassengerVehiclePickDrop\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_passenger_vehicle_pick_drop__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentStop,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            type: currentEventType.value,\n                            logBookConfig: logBookConfig,\n                            members: crewMembers,\n                            locked: locked || !edit_tripActivity,\n                            tripReport_Stops: tripReport_Stops,\n                            setTripReport_Stops: setTripReport_Stops,\n                            displayDangerousGoods: displayDangerousGoodsPvpd,\n                            setDisplayDangerousGoods: setDisplayDangerousGoodsPvpd,\n                            displayDangerousGoodsSailing: displayDangerousGoodsPvpdSailing,\n                            setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsPvpdSailing,\n                            allPVPDDangerousGoods: allPVPDDangerousGoods,\n                            setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                            selectedDGR: selectedDGRPVPD,\n                            setSelectedDGR: setSelectedDGRPVPD\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1859,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"RefuellingBunkering\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_refuelling_bunkering__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_tripActivity,\n                            mainFuelLogs: fuelLogs\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1896,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"TripUpdate\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_trip_update__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            locked: locked || !edit_tripActivity\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1911,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"InfringementNotice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_InfringementNotices__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            crewMembers: crewMembers,\n                            locked: locked || !edit_tripActivity,\n                            visibility: currentEventType && !currentEvent && !currentStop\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1938,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"IncidentRecord\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_incident_record_incident_record_form__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                            id: (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.incidentRecordID) || 0,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            closeModal: handleSetCurrentEventType,\n                            inLogbook: true,\n                            selectedEvent: currentEvent,\n                            offline: offline,\n                            tripReport: tripReport\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1957,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false)\n                ]\n            }, void 0, true),\n            currentTrip.tripReportScheduleID > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radio_logs_schedule__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                open: displayRadioLogs,\n                setOpen: setDisplayRadioLogs,\n                currentTrip: currentTrip\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                lineNumber: 1972,\n                columnNumber: 17\n            }, this)\n        ]\n    }, lodash_uniqueId__WEBPACK_IMPORTED_MODULE_14___default()(), true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n        lineNumber: 864,\n        columnNumber: 9\n    }, this);\n}\n_s(Events, \"kBeEJRvj+liqmcuZ7jwYORdkupw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useSearchParams,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_30__.useBreakpoints\n    ];\n});\n_c = Events;\nvar _c;\n$RefreshReg$(_c, \"Events\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/events.tsx\n"));

/***/ })

});