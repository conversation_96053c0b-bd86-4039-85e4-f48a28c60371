"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/events.tsx":
/*!***************************************!*\
  !*** ./src/app/ui/logbook/events.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Events; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _forms_vessel_rescue__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./forms/vessel-rescue */ \"(app-pages-browser)/./src/app/ui/logbook/forms/vessel-rescue.tsx\");\n/* harmony import */ var _forms_person_rescue__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./forms/person-rescue */ \"(app-pages-browser)/./src/app/ui/logbook/forms/person-rescue.tsx\");\n/* harmony import */ var _forms_restricted_visibility__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./forms/restricted-visibility */ \"(app-pages-browser)/./src/app/ui/logbook/forms/restricted-visibility.tsx\");\n/* harmony import */ var _forms_bar_crossing__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./forms/bar-crossing */ \"(app-pages-browser)/./src/app/ui/logbook/forms/bar-crossing.tsx\");\n/* harmony import */ var _forms_passenger_drop_facility__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./forms/passenger-drop-facility */ \"(app-pages-browser)/./src/app/ui/logbook/forms/passenger-drop-facility.tsx\");\n/* harmony import */ var _forms_tasking__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./forms/tasking */ \"(app-pages-browser)/./src/app/ui/logbook/forms/tasking.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _forms_crew_training_event__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./forms/crew-training-event */ \"(app-pages-browser)/./src/app/ui/logbook/forms/crew-training-event.tsx\");\n/* harmony import */ var _forms_supernumerary_event__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./forms/supernumerary-event */ \"(app-pages-browser)/./src/app/ui/logbook/forms/supernumerary-event.tsx\");\n/* harmony import */ var _forms_passenger_vehicle_pick_drop__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./forms/passenger-vehicle-pick-drop */ \"(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx\");\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! lodash/uniqueId */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/uniqueId.js\");\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var _forms_refuelling_bunkering__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./forms/refuelling-bunkering */ \"(app-pages-browser)/./src/app/ui/logbook/forms/refuelling-bunkering.tsx\");\n/* harmony import */ var _app_lib_vesselTypes__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/lib/vesselTypes */ \"(app-pages-browser)/./src/app/lib/vesselTypes.tsx\");\n/* harmony import */ var _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/app/lib/logbook-configuration */ \"(app-pages-browser)/./src/app/lib/logbook-configuration/index.ts\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _forms_InfringementNotices__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./forms/InfringementNotices */ \"(app-pages-browser)/./src/app/ui/logbook/forms/InfringementNotices.tsx\");\n/* harmony import */ var _forms_trip_update__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./forms/trip-update */ \"(app-pages-browser)/./src/app/ui/logbook/forms/trip-update.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_21__);\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/accordion */ \"(app-pages-browser)/./src/components/ui/accordion.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _radio_logs_schedule__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./radio-logs-schedule */ \"(app-pages-browser)/./src/app/ui/logbook/radio-logs-schedule.tsx\");\n/* harmony import */ var _incident_record_incident_record_form__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ../incident-record/incident-record-form */ \"(app-pages-browser)/./src/app/ui/incident-record/incident-record-form.tsx\");\n/* harmony import */ var _forms_pilot_transfer__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./forms/pilot-transfer */ \"(app-pages-browser)/./src/app/ui/logbook/forms/pilot-transfer.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Events(param) {\n    let { currentTrip, logBookConfig, updateTripReport, locked, geoLocations, tripReport, crewMembers, masterID, vessel, vessels, offline = false, setSelectedRow, setCurrentEventType, setCurrentStop, currentEventType, currentStop, tripReport_Stops, setTripReport_Stops, displayDangerousGoodsPvpd = false, displayDangerousGoodsPvpdSailing, setDisplayDangerousGoodsPvpd, setDisplayDangerousGoodsPvpdSailing, allPVPDDangerousGoods, setAllPVPDDangerousGoods, selectedDGRPVPD, setSelectedDGRPVPD, fuelLogs, logBookStartDate } = param;\n    var _currentTrip_tripReport_Stops, _currentTrip_tripEvents_nodes, _currentTrip_tripEvents, _currentTrip_tripReport_Stops_nodes, _currentTrip_tripReport_Stops1, _currentTrip_tripEvents_nodes1, _currentTrip_tripEvents1, _currentTrip_tripReport_Stops_nodes1, _currentTrip_tripReport_Stops2, _currentTrip_tripReport_Stops3;\n    _s();\n    const [events, setEvents] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [openEventModal, setOpenEventModal] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [taskingEvents, setTaskingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const [accordionValue, setAccordionValue] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const vesselID = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useSearchParams)().get(\"vesselID\") || \"0\";\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [edit_tripActivity, setEdit_tripActivity] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [displayRadioLogs, setDisplayRadioLogs] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [activityTypeOptions, setActivityTypeOptions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__.hasPermission)(process.env.EDIT_LOGBOOKENTRY_ACTIVITY || \"EDIT_LOGBOOKENTRY_ACTIVITY\", permissions)) {\n                setEdit_tripActivity(true);\n            } else {\n                setEdit_tripActivity(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        init_permissions();\n        initData();\n    }, [\n        permissions\n    ]);\n    const initData = ()=>{\n        var _logBookConfig_customisedLogBookComponents;\n        const combinedFields = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents.nodes.filter((section)=>section.componentClass === \"SeaLogs\\\\EventType_LogBookComponent\" || section.componentClass === \"EventType_LogBookComponent\").reduce((acc, section)=>{\n            acc = acc.concat(section.customisedComponentFields.nodes);\n            return acc;\n        }, []);\n        const hasRescueType = combinedFields === null || combinedFields === void 0 ? void 0 : combinedFields.find((field)=>field.fieldName === \"VesselRescue\" || field.fieldName === \"HumanRescue\");\n        if (logBookConfig) {\n            const eventList = hasRescueType ? combinedFields.filter((field)=>!hasParent(field) && field.status !== \"Off\") : combinedFields === null || combinedFields === void 0 ? void 0 : combinedFields.filter((field)=>!hasParent(field) && field.status !== \"Off\" && field.fieldName !== \"TaskingStartUnderway\" && field.fieldName !== \"TaskingOnScene\" && field.fieldName !== \"TaskingOnTow\" && field.fieldName !== \"TaskingPaused\" && field.fieldName !== \"TaskingResumed\" && field.fieldName !== \"TaskingComplete\" && field.fieldName !== \"DangerousGoodsSailing\");\n            const filteredEvents = eventList === null || eventList === void 0 ? void 0 : eventList.map((event)=>({\n                    label: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_2__.getFieldName)(event).replace(/([a-z])([A-Z])/g, \"$1 $2\").replace(\"Passenger Arrival\", \"Arrival\").replace(\"Passenger Departure\", \"Departure\"),\n                    value: event.fieldName\n                })).filter((event, index, self)=>index === self.findIndex((e)=>e.value === event.value)).filter((event)=>// event?.value !== 'VesselRescue' &&\n                // event?.value !== 'HumanRescue' &&\n                // event?.value !== 'Supernumerary' &&\n                !isTowingField(event.value)).filter((event)=>checkVesselType(event.value));\n            // Add Incident Record as a custom activity type\n            // Incident Record is available for all vessel types\n            filteredEvents.push({\n                label: \"Incident Record\",\n                value: \"IncidentRecord\"\n            });\n            // Add Infringement Notices as a custom activity type if vessel type allows it\n            // InfringementNotices is only available for vessel types 0 and 1\n            const vesselTypeID = _app_lib_vesselTypes__WEBPACK_IMPORTED_MODULE_16__[\"default\"].findIndex((type)=>type == (vessel === null || vessel === void 0 ? void 0 : vessel.vesselType));\n            if ([\n                0,\n                1\n            ].includes(vesselTypeID)) {\n                filteredEvents.push({\n                    label: \"Infringement Notices\",\n                    value: \"InfringementNotice\"\n                });\n            }\n            if (permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__.hasPermission)(\"RECORD_TRAINING\", permissions)) {\n                setEvents(sortFilteredEvents(filteredEvents));\n            } else {\n                var _filteredEvents_filter;\n                setEvents(sortFilteredEvents((_filteredEvents_filter = filteredEvents === null || filteredEvents === void 0 ? void 0 : filteredEvents.filter((event)=>event.value !== \"CrewTraining\")) !== null && _filteredEvents_filter !== void 0 ? _filteredEvents_filter : []));\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        initData();\n    }, [\n        logBookConfig\n    ]);\n    const checkVesselType = (field)=>{\n        const vesselTypeID = _app_lib_vesselTypes__WEBPACK_IMPORTED_MODULE_16__[\"default\"].findIndex((type)=>type == (vessel === null || vessel === void 0 ? void 0 : vessel.vesselType));\n        const config = _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_17__.SLALL_LogBookFields.find((localField)=>localField.componentClass === \"EventType_LogBookComponent\");\n        const isVesselType = config === null || config === void 0 ? void 0 : config.items.find((localField)=>field === localField.value && localField.vesselType.includes(vesselTypeID));\n        return isVesselType ? true : false;\n    };\n    const sortFilteredEvents = (events)=>{\n        var _currentTrip_tripEvents_nodes, _currentTrip_tripEvents;\n        // Always ensure tasking events are available in the base events array\n        const taskingEvents = [\n            \"TaskingStartUnderway\",\n            \"TaskingOnScene\",\n            \"TaskingOnTow\",\n            \"TaskingComplete\",\n            \"TaskingPaused\",\n            \"TaskingResumed\"\n        ];\n        // Add missing tasking events to the events array\n        const eventsWithTasking = [\n            ...events\n        ];\n        taskingEvents.forEach((taskingType)=>{\n            if (!eventsWithTasking.find((event)=>event.value === taskingType)) {\n                eventsWithTasking.push({\n                    label: taskingType.replace(/([a-z])([A-Z])/g, \"$1 $2\"),\n                    value: taskingType\n                });\n            }\n        });\n        if (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes = _currentTrip_tripEvents.nodes) === null || _currentTrip_tripEvents_nodes === void 0 ? void 0 : _currentTrip_tripEvents_nodes.find((event)=>{\n            var _event_eventType_Tasking, _event_eventType_Tasking1;\n            return event.eventCategory === \"Tasking\" && ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\" && ((_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.status) === \"Open\";\n        })) {\n            var _currentTrip_tripEvents_nodes_filter, _currentTrip_tripEvents_nodes_filter1;\n            const openTask = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_filter = currentTrip.tripEvents.nodes.filter((event)=>{\n                var _event_eventType_Tasking;\n                return event.eventCategory === \"Tasking\" && ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\";\n            })) === null || _currentTrip_tripEvents_nodes_filter === void 0 ? void 0 : _currentTrip_tripEvents_nodes_filter.length;\n            const pausedTask = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_filter1 = currentTrip.tripEvents.nodes.filter((event)=>{\n                var _event_eventType_Tasking;\n                return event.eventCategory === \"Tasking\" && ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingPaused\";\n            })) === null || _currentTrip_tripEvents_nodes_filter1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_filter1.length;\n            const sortedEvents = [\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingStartUnderway\" && openTask - pausedTask < 1).map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingOnScene\").map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingOnTow\").map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingComplete\").map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingPaused\").map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingResumed\" && pausedTask > 0).map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>!event.value.includes(\"Tasking\"))\n            ];\n            return sortedEvents;\n        }\n        return eventsWithTasking;\n    };\n    /*const colourStyles: StylesConfig = {\r\n        option: (\r\n            styles: any,\r\n            {\r\n                data,\r\n                isDisabled,\r\n                isFocused,\r\n                isSelected,\r\n            }: { data: any; isDisabled: any; isFocused: any; isSelected: any },\r\n        ) => {\r\n            const color = data.color\r\n            return {\r\n                ...styles,\r\n                backgroundColor: isDisabled\r\n                    ? undefined\r\n                    : isSelected\r\n                      ? data.bgColor\r\n                      : isFocused\r\n                        ? data.bgColor\r\n                        : data.bgColor + '60',\r\n                color: data.color,\r\n            }\r\n        },\r\n        singleValue: (styles: any, data: any) => ({\r\n            ...styles,\r\n            color: events.find((option: any) => option.value == data.data.value)\r\n                ?.color,\r\n        }),\r\n    }*/ const formatTime = (time)=>time.slice(0, 5);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        var _currentTrip_tripEvents_nodes_filter, _currentTrip_tripEvents;\n        const taskingEvents = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_filter = _currentTrip_tripEvents.nodes.filter((event)=>{\n            var _event_eventType_Tasking, _event_eventType_Tasking1, _event_eventType_Tasking2;\n            return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && ((event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\" || (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.type) === \"TaskingPaused\") && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.status) === \"Open\";\n        })) === null || _currentTrip_tripEvents_nodes_filter === void 0 ? void 0 : _currentTrip_tripEvents_nodes_filter.length;\n        setTaskingEvents(taskingEvents);\n    }, [\n        currentTrip\n    ]);\n    const hasParent = (field)=>{\n        const config = _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_17__.SLALL_LogBookFields.find((localField)=>localField.componentClass === \"EventType_LogBookComponent\");\n        const hasGroup = config === null || config === void 0 ? void 0 : config.items.find((localField)=>field.fieldName === localField.value && localField.groupTo);\n        return hasGroup ? true : false;\n    };\n    const isTowingField = (field)=>{\n        const config = _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_17__.SLALL_LogBookFields.find((localField)=>localField.componentClass === \"EventType_LogBookComponent\");\n        const isTowingCategory = config === null || config === void 0 ? void 0 : config.items.find((localField)=>field === localField.value && localField.type === \"TowingSubCategory\");\n        return isTowingCategory ? true : false;\n    };\n    const handleEventChange = (event)=>{\n        setCurrentEvent(false);\n        setCurrentStop(false);\n        setTripReport_Stops(false);\n        setDisplayDangerousGoodsPvpd(false);\n        setDisplayDangerousGoodsPvpdSailing(null);\n        fetchActivityTypes();\n        setCurrentEventType(event);\n    };\n    // const handleSetOpenEventModal = () => {\n    // setOpenEventModal(!openEventModal)\n    // }\n    const handleSetCurrentEventType = ()=>{\n        setCurrentEventType(false);\n        // Reset accordion state to properly close it\n        setAccordionValue(\"\");\n        setSelectedRow(0);\n        setCurrentEvent(false);\n        setCurrentStop(false);\n    // setOpenEventModal(false)\n    };\n    const previousDropEvent = (currentEvent)=>{\n        var _currentTrip_tripEvents;\n        const previousEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" && (event === null || event === void 0 ? void 0 : event.id) !== (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id));\n        return previousEvent;\n    };\n    const mainTaskingEvent = (currentEvent)=>{\n        var _currentTrip_tripEvents;\n        const mainEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.filter((event)=>{\n            var _event_eventType_Tasking;\n            return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : event.id) !== (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\";\n        });\n        return mainEvent;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (events) {\n            var _currentTrip_tripEvents_nodes_filter, _currentTrip_tripEvents_nodes, _currentTrip_tripEvents;\n            let options = [];\n            if (taskingEvents === 0) {\n                options = events.filter((event)=>(event === null || event === void 0 ? void 0 : event.value) !== \"TaskingOnScene\" && (event === null || event === void 0 ? void 0 : event.value) !== \"TaskingOnTow\" && (event === null || event === void 0 ? void 0 : event.value) !== \"TaskingPaused\" && (event === null || event === void 0 ? void 0 : event.value) !== \"TaskingResumed\" && (event === null || event === void 0 ? void 0 : event.value) !== \"TaskingComplete\");\n                // Ensure TaskingStartUnderway is always available when no tasking events are open\n                const taskingStartUnderwayOption = events.find((event)=>(event === null || event === void 0 ? void 0 : event.value) === \"TaskingStartUnderway\");\n                if (taskingStartUnderwayOption && !options.find((option)=>option.value === \"TaskingStartUnderway\")) {\n                    options.push(taskingStartUnderwayOption);\n                } else if (!taskingStartUnderwayOption) {\n                    // If TaskingStartUnderway is not in the events array, create it manually\n                    options.push({\n                        label: \"Tasking Start Underway\",\n                        value: \"TaskingStartUnderway\"\n                    });\n                }\n            } else {\n                var _currentTrip_tripEvents1, _currentTrip_tripEvents2, _currentTrip_tripEvents3;\n                const taskingOpen = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : _currentTrip_tripEvents1.nodes.filter((event)=>{\n                    var _event_eventType_Tasking, _event_eventType_Tasking1;\n                    return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.status) === \"Open\";\n                });\n                const taskingPaused = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents2 = currentTrip.tripEvents) === null || _currentTrip_tripEvents2 === void 0 ? void 0 : _currentTrip_tripEvents2.nodes.filter((event)=>{\n                    var _event_eventType_Tasking;\n                    return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingPaused\";\n                });\n                const taskingResumed = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents3 = currentTrip.tripEvents) === null || _currentTrip_tripEvents3 === void 0 ? void 0 : _currentTrip_tripEvents3.nodes.filter((event)=>{\n                    var _event_eventType_Tasking;\n                    return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingResumed\";\n                });\n                if ((taskingOpen === null || taskingOpen === void 0 ? void 0 : taskingOpen.length) > 0) {\n                    if ((taskingPaused === null || taskingPaused === void 0 ? void 0 : taskingPaused.length) === (taskingResumed === null || taskingResumed === void 0 ? void 0 : taskingResumed.length)) {\n                        options = events.filter((event)=>(event === null || event === void 0 ? void 0 : event.value) !== \"TaskingResumed\");\n                    }\n                    if ((taskingPaused === null || taskingPaused === void 0 ? void 0 : taskingPaused.length) > (taskingResumed === null || taskingResumed === void 0 ? void 0 : taskingResumed.length)) {\n                        options = events.filter((event)=>event.value !== \"TaskingOnScene\" && event.value !== \"TaskingOnTow\" && event.value !== \"TaskingPaused\");\n                    }\n                } else {\n                    options = events.filter((event)=>event.value !== \"TaskingOnScene\" && event.value !== \"TaskingOnTow\" && event.value !== \"TaskingPaused\" && event.value !== \"TaskingResumed\" && event.value !== \"TaskingComplete\");\n                    // Ensure TaskingStartUnderway is available when no open tasking events exist\n                    const taskingStartUnderwayOption = events.find((event)=>(event === null || event === void 0 ? void 0 : event.value) === \"TaskingStartUnderway\");\n                    if (taskingStartUnderwayOption && !options.find((option)=>option.value === \"TaskingStartUnderway\")) {\n                        options.push(taskingStartUnderwayOption);\n                    } else if (!taskingStartUnderwayOption) {\n                        // If TaskingStartUnderway is not in the events array, create it manually\n                        options.push({\n                            label: \"Tasking Start Underway\",\n                            value: \"TaskingStartUnderway\"\n                        });\n                    }\n                }\n            }\n            // When taskingPaused > 0, ensure TaskingResumed and TaskingStartUnderway are available\n            const taskingPausedCount = (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes = _currentTrip_tripEvents.nodes) === null || _currentTrip_tripEvents_nodes === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_filter = _currentTrip_tripEvents_nodes.filter((event)=>{\n                var _event_eventType_Tasking;\n                return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingPaused\";\n            })) === null || _currentTrip_tripEvents_nodes_filter === void 0 ? void 0 : _currentTrip_tripEvents_nodes_filter.length) || 0;\n            if (taskingPausedCount > 0) {\n                // Find TaskingResumed and TaskingStartUnderway from the original events array\n                const taskingResumedOption = events.find((event)=>(event === null || event === void 0 ? void 0 : event.value) === \"TaskingResumed\");\n                const taskingStartUnderwayOption = events.find((event)=>(event === null || event === void 0 ? void 0 : event.value) === \"TaskingStartUnderway\");\n                // Add TaskingResumed if it exists in events but not in current options\n                if (taskingResumedOption && !options.find((option)=>option.value === \"TaskingResumed\")) {\n                    options.push(taskingResumedOption);\n                }\n                // Add TaskingStartUnderway if it exists in events but not in current options\n                if (taskingStartUnderwayOption && !options.find((option)=>option.value === \"TaskingStartUnderway\")) {\n                    options.push(taskingStartUnderwayOption);\n                } else if (!taskingStartUnderwayOption) {\n                    // If TaskingStartUnderway is not in the events array, create it manually\n                    options.push({\n                        label: \"Tasking Start Underway\",\n                        value: \"TaskingStartUnderway\"\n                    });\n                }\n            }\n            options = options.map((option)=>{\n                if (option.value.includes(\"Tasking\") && option.value !== \"TaskingStartUnderway\" && !option.className) {\n                    return {\n                        ...option,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    };\n                }\n                return option;\n            });\n            // Remove duplicate by checking the options.value\n            options = options.filter((option, index, self)=>index === self.findIndex((o)=>o.value === option.value));\n            // Remove InfringementNotices from options because there's already InfringementNotice (without an 's').\n            options = options.filter((option)=>option.value !== \"InfringementNotices\");\n            // Remove HumanRescue and VesselRescue from options since it's already included in Tasking\n            options = options.filter((option)=>option.value !== \"HumanRescue\" && option.value !== \"VesselRescue\");\n            setActivityTypeOptions(options);\n        }\n    }, [\n        events,\n        currentTrip,\n        taskingEvents\n    ]);\n    const fetchActivityTypes = ()=>{\n        initData();\n    };\n    // Memoized function to handle stop accordion item clicks\n    const handleStopAccordionItemClick = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((eventId)=>{\n        return ()=>{\n            // Toggle accordion state\n            if (accordionValue === \"stop_\".concat(eventId)) {\n                setAccordionValue(\"\");\n                setSelectedRow(0);\n                setCurrentEventType([]);\n                setCurrentEvent(false);\n                setCurrentStop(false);\n            } else {\n                var _currentTrip_tripReport_Stops;\n                setAccordionValue(\"stop_\".concat(eventId));\n                setSelectedRow(eventId);\n                setCurrentEventType({\n                    label: \"Passenger/vehicle pickup/drop off\",\n                    value: \"PassengerVehiclePickDrop\"\n                });\n                // Find the event by ID\n                const event = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops === void 0 ? void 0 : _currentTrip_tripReport_Stops.nodes.find((stop)=>stop.id === eventId);\n                setCurrentStop(event);\n                setDisplayDangerousGoodsPvpd(false);\n                setDisplayDangerousGoodsPvpdSailing(null);\n                setTripReport_Stops(false);\n            }\n        };\n    }, [\n        accordionValue,\n        currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops === void 0 ? void 0 : _currentTrip_tripReport_Stops.nodes,\n        setSelectedRow,\n        setCurrentEventType,\n        setCurrentEvent,\n        setCurrentStop,\n        setDisplayDangerousGoodsPvpd,\n        setDisplayDangerousGoodsPvpdSailing,\n        setTripReport_Stops\n    ]);\n    // Memoized function to generate stop display text\n    const getStopDisplayText = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((event)=>{\n        var _event_stopLocation, _event_stopLocation1;\n        return \"Passenger / Vehicle Pick & Drop - \".concat((event === null || event === void 0 ? void 0 : event.arriveTime) ? (event === null || event === void 0 ? void 0 : event.arriveTime) + \" (arr)\" : \"\", \" \").concat((event === null || event === void 0 ? void 0 : event.arriveTime) && (event === null || event === void 0 ? void 0 : event.departTime) ? \"-\" : \"\", \" \").concat((event === null || event === void 0 ? void 0 : event.departTime) ? (event === null || event === void 0 ? void 0 : event.departTime) + \" (dep)\" : \"\", \" \").concat((event === null || event === void 0 ? void 0 : (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.title) ? event === null || event === void 0 ? void 0 : (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.title : \"\");\n    }, []);\n    const shouldIndent = (event)=>{\n        var _event_eventType_Tasking;\n        return event.eventCategory === \"Tasking\" && ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) !== \"TaskingStartUnderway\";\n    };\n    const getEventLabel = (event)=>{\n        var _event_eventType_PassengerDropFacility_type_replace_replace, _event_eventType_PassengerDropFacility_type_replace, _event_eventType_PassengerDropFacility_type, _event_eventType_PassengerDropFacility, _event_eventType_Tasking;\n        return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" ? event === null || event === void 0 ? void 0 : (_event_eventType_PassengerDropFacility = event.eventType_PassengerDropFacility) === null || _event_eventType_PassengerDropFacility === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type = _event_eventType_PassengerDropFacility.type) === null || _event_eventType_PassengerDropFacility_type === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type_replace = _event_eventType_PassengerDropFacility_type.replace(/([a-z])([A-Z])/g, \"$1 $2\")) === null || _event_eventType_PassengerDropFacility_type_replace === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type_replace_replace = _event_eventType_PassengerDropFacility_type_replace.replace(\"Passenger Arrival\", \"Arrival\")) === null || _event_eventType_PassengerDropFacility_type_replace_replace === void 0 ? void 0 : _event_eventType_PassengerDropFacility_type_replace_replace.replace(\"Passenger Departure\", \"Departure\") : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" ? event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type.replace(/([a-z])([A-Z])/g, \"$1 $2\") : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"EventSupernumerary\" ? \"Supernumerary\" : event === null || event === void 0 ? void 0 : event.eventCategory.replace(/([a-z])([A-Z])/g, \"$1 $2\");\n    };\n    const getEventValue = (event)=>{\n        var _event_eventType_PassengerDropFacility, _event_eventType_Tasking;\n        return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" ? event === null || event === void 0 ? void 0 : (_event_eventType_PassengerDropFacility = event.eventType_PassengerDropFacility) === null || _event_eventType_PassengerDropFacility === void 0 ? void 0 : _event_eventType_PassengerDropFacility.type : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" ? event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Supernumerary\" ? \"EventSupernumerary\" : event === null || event === void 0 ? void 0 : event.eventCategory;\n    };\n    const getFuelTotals = (fuelLogs)=>{\n        const totalFuel = fuelLogs.reduce((acc, log)=>{\n            return acc + (log === null || log === void 0 ? void 0 : log.fuelAdded);\n        }, 0);\n        const totalCost = fuelLogs.reduce((acc, log)=>{\n            return acc + (log === null || log === void 0 ? void 0 : log.fuelAdded) * (log === null || log === void 0 ? void 0 : log.costPerLitre);\n        }, 0);\n        return \" - Total Fuel Added: \" + totalFuel + \"L, Total Cost: $\" + totalCost;\n    };\n    const getEventDisplayText = (event)=>{\n        var _eventType_geoLocation;\n        const category = event.eventCategory;\n        const eventType = event[\"eventType_\".concat(category)];\n        const geoLocation = eventType === null || eventType === void 0 ? void 0 : (_eventType_geoLocation = eventType.geoLocation) === null || _eventType_geoLocation === void 0 ? void 0 : _eventType_geoLocation.title;\n        const title = eventType === null || eventType === void 0 ? void 0 : eventType.title;\n        switch(category){\n            case \"PassengerDropFacility\":\n                var _eventType_type_replace_replace, _eventType_type_replace, _eventType_type;\n                return ((eventType === null || eventType === void 0 ? void 0 : eventType.time) ? eventType.time + \" - \" : \"\") + (eventType === null || eventType === void 0 ? void 0 : (_eventType_type = eventType.type) === null || _eventType_type === void 0 ? void 0 : (_eventType_type_replace = _eventType_type.replace(/([a-z])([A-Z])/g, \"$1 $2\")) === null || _eventType_type_replace === void 0 ? void 0 : (_eventType_type_replace_replace = _eventType_type_replace.replace(\"Passenger Arrival\", \"Arrival\")) === null || _eventType_type_replace_replace === void 0 ? void 0 : _eventType_type_replace_replace.replace(\"Passenger Departure\", \"Departure\")) + (title ? \" - \" + title : \"\") + (geoLocation ? \" - \" + geoLocation : \"\");\n            case \"Tasking\":\n                var _eventType_type1;\n                return (eventType === null || eventType === void 0 ? void 0 : eventType.time) + \" - \" + (eventType === null || eventType === void 0 ? void 0 : (_eventType_type1 = eventType.type) === null || _eventType_type1 === void 0 ? void 0 : _eventType_type1.replace(/([a-z])([A-Z])/g, \"$1 $2\")) + (title ? \" - \" + title : \"\") + (geoLocation ? \" - \" + geoLocation : \"\");\n            case \"BarCrossing\":\n                return ((eventType === null || eventType === void 0 ? void 0 : eventType.time) ? eventType.time + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (geoLocation ? \" - \" + geoLocation : \"\");\n            case \"RefuellingBunkering\":\n                return ((eventType === null || eventType === void 0 ? void 0 : eventType.date) ? dayjs__WEBPACK_IMPORTED_MODULE_21___default()(eventType.date).format(\"HH:mm\") + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (geoLocation ? \" - \" + geoLocation : \"\");\n            case \"RestrictedVisibility\":\n                var _eventType_startLocation;\n                return ((eventType === null || eventType === void 0 ? void 0 : eventType.crossingTime) ? eventType.crossingTime + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + ((eventType === null || eventType === void 0 ? void 0 : (_eventType_startLocation = eventType.startLocation) === null || _eventType_startLocation === void 0 ? void 0 : _eventType_startLocation.title) ? \" - \" + eventType.startLocation.title : \"\");\n            case \"TripUpdate\":\n                var _event_tripUpdate, _event_tripUpdate_geoLocation, _event_tripUpdate1, _event_tripUpdate_geoLocation1, _event_tripUpdate2;\n                return (((_event_tripUpdate = event.tripUpdate) === null || _event_tripUpdate === void 0 ? void 0 : _event_tripUpdate.date) ? dayjs__WEBPACK_IMPORTED_MODULE_21___default()(event.tripUpdate.date).format(\"HH:mm\") + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (((_event_tripUpdate1 = event.tripUpdate) === null || _event_tripUpdate1 === void 0 ? void 0 : (_event_tripUpdate_geoLocation = _event_tripUpdate1.geoLocation) === null || _event_tripUpdate_geoLocation === void 0 ? void 0 : _event_tripUpdate_geoLocation.title) ? \" - \" + ((_event_tripUpdate2 = event.tripUpdate) === null || _event_tripUpdate2 === void 0 ? void 0 : (_event_tripUpdate_geoLocation1 = _event_tripUpdate2.geoLocation) === null || _event_tripUpdate_geoLocation1 === void 0 ? void 0 : _event_tripUpdate_geoLocation1.title) : \"\");\n            case \"EventSupernumerary\":\n                var _event_supernumerary, _event_supernumerary1, _event_supernumerary2;\n                return ((event === null || event === void 0 ? void 0 : (_event_supernumerary = event.supernumerary) === null || _event_supernumerary === void 0 ? void 0 : _event_supernumerary.briefingTime) ? (event === null || event === void 0 ? void 0 : (_event_supernumerary1 = event.supernumerary) === null || _event_supernumerary1 === void 0 ? void 0 : _event_supernumerary1.briefingTime) + \" - \" : \"\") + \"Supernumerary\" + ((event === null || event === void 0 ? void 0 : (_event_supernumerary2 = event.supernumerary) === null || _event_supernumerary2 === void 0 ? void 0 : _event_supernumerary2.title) ? \" - \" + (event === null || event === void 0 ? void 0 : event.supernumerary.title) : \"\");\n            case \"IncidentRecord\":\n                var _event_incidentRecord, _event_incidentRecord1;\n                return (((_event_incidentRecord = event.incidentRecord) === null || _event_incidentRecord === void 0 ? void 0 : _event_incidentRecord.startDate) ? dayjs__WEBPACK_IMPORTED_MODULE_21___default()(event.incidentRecord.startDate).format(\"HH:mm\") + \" - \" : \"\") + \"Incident Record\" + (((_event_incidentRecord1 = event.incidentRecord) === null || _event_incidentRecord1 === void 0 ? void 0 : _event_incidentRecord1.title) ? \" - \" + event.incidentRecord.title : \"\");\n            case \"InfringementNotice\":\n                var _event_infringementNotice, _event_infringementNotice1, _event_infringementNotice2;\n                return ((event === null || event === void 0 ? void 0 : (_event_infringementNotice = event.infringementNotice) === null || _event_infringementNotice === void 0 ? void 0 : _event_infringementNotice.time) ? dayjs__WEBPACK_IMPORTED_MODULE_21___default()(\"\".concat(dayjs__WEBPACK_IMPORTED_MODULE_21___default()().format(\"YYYY-MM-DD\"), \" \").concat(event === null || event === void 0 ? void 0 : event.infringementNotice.time)).format(\"HH:mm\") + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + ((event === null || event === void 0 ? void 0 : (_event_infringementNotice1 = event.infringementNotice) === null || _event_infringementNotice1 === void 0 ? void 0 : _event_infringementNotice1.geoLocation.title) ? \" - \" + (event === null || event === void 0 ? void 0 : (_event_infringementNotice2 = event.infringementNotice) === null || _event_infringementNotice2 === void 0 ? void 0 : _event_infringementNotice2.geoLocation.title) : \"\");\n            case \"CrewTraining\":\n                var _event_crewTraining, _event_crewTraining_geoLocation, _event_crewTraining1, _event_crewTraining_geoLocation1, _event_crewTraining2;\n                return (((_event_crewTraining = event.crewTraining) === null || _event_crewTraining === void 0 ? void 0 : _event_crewTraining.startTime) ? event.crewTraining.startTime + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (((_event_crewTraining1 = event.crewTraining) === null || _event_crewTraining1 === void 0 ? void 0 : (_event_crewTraining_geoLocation = _event_crewTraining1.geoLocation) === null || _event_crewTraining_geoLocation === void 0 ? void 0 : _event_crewTraining_geoLocation.title) ? \" - \" + ((_event_crewTraining2 = event.crewTraining) === null || _event_crewTraining2 === void 0 ? void 0 : (_event_crewTraining_geoLocation1 = _event_crewTraining2.geoLocation) === null || _event_crewTraining_geoLocation1 === void 0 ? void 0 : _event_crewTraining_geoLocation1.title) : \"\");\n            case \"VesselRescue\":\n                var _event_eventType_VesselRescue_mission, _event_eventType_VesselRescue, _event_eventType_VesselRescue_mission1, _event_eventType_VesselRescue1, _event_eventType_VesselRescue2;\n                return (((_event_eventType_VesselRescue = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue === void 0 ? void 0 : (_event_eventType_VesselRescue_mission = _event_eventType_VesselRescue.mission) === null || _event_eventType_VesselRescue_mission === void 0 ? void 0 : _event_eventType_VesselRescue_mission.completedAt) ? ((_event_eventType_VesselRescue1 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue1 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission1 = _event_eventType_VesselRescue1.mission) === null || _event_eventType_VesselRescue_mission1 === void 0 ? void 0 : _event_eventType_VesselRescue_mission1.completedAt) + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (((_event_eventType_VesselRescue2 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue2 === void 0 ? void 0 : _event_eventType_VesselRescue2.vesselName) ? \" - \" + event.eventType_VesselRescue.vesselName : \"\");\n            case \"HumanRescue\":\n                var _event_eventType_PersonRescue_mission, _event_eventType_PersonRescue, _event_eventType_PersonRescue_mission1, _event_eventType_PersonRescue1, _event_eventType_PersonRescue2;\n                return (((_event_eventType_PersonRescue = event.eventType_PersonRescue) === null || _event_eventType_PersonRescue === void 0 ? void 0 : (_event_eventType_PersonRescue_mission = _event_eventType_PersonRescue.mission) === null || _event_eventType_PersonRescue_mission === void 0 ? void 0 : _event_eventType_PersonRescue_mission.completedAt) ? ((_event_eventType_PersonRescue1 = event.eventType_PersonRescue) === null || _event_eventType_PersonRescue1 === void 0 ? void 0 : (_event_eventType_PersonRescue_mission1 = _event_eventType_PersonRescue1.mission) === null || _event_eventType_PersonRescue_mission1 === void 0 ? void 0 : _event_eventType_PersonRescue_mission1.completedAt) + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (((_event_eventType_PersonRescue2 = event.eventType_PersonRescue) === null || _event_eventType_PersonRescue2 === void 0 ? void 0 : _event_eventType_PersonRescue2.personName) ? \" - \" + event.eventType_PersonRescue.personName : \"\");\n            default:\n                return category.replace(/([a-z])([A-Z])/g, \"$1 $2\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row justify-between items-end\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_25__.H4, {\n                            children: \"ACTIVITIES\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 864,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_25__.P, {\n                            children: \"Record the events that happen during a voyage in this section.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 865,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                    lineNumber: 863,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                lineNumber: 862,\n                columnNumber: 13\n            }, this),\n            (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes = _currentTrip_tripEvents.nodes) === null || _currentTrip_tripEvents_nodes === void 0 ? void 0 : _currentTrip_tripEvents_nodes.length) > 0 || (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops1 = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops1 === void 0 ? void 0 : (_currentTrip_tripReport_Stops_nodes = _currentTrip_tripReport_Stops1.nodes) === null || _currentTrip_tripReport_Stops_nodes === void 0 ? void 0 : _currentTrip_tripReport_Stops_nodes.length) > 0 || !currentEvent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: ((currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes1 = _currentTrip_tripEvents1.nodes) === null || _currentTrip_tripEvents_nodes1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes1.length) > 0 || (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops2 = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops2 === void 0 ? void 0 : (_currentTrip_tripReport_Stops_nodes1 = _currentTrip_tripReport_Stops2.nodes) === null || _currentTrip_tripReport_Stops_nodes1 === void 0 ? void 0 : _currentTrip_tripReport_Stops_nodes1.length) > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.Accordion, {\n                    type: \"single\",\n                    collapsible: true,\n                    value: accordionValue,\n                    onValueChange: (value)=>{\n                        setAccordionValue(value);\n                        // If we're closing the accordion, reset the state\n                        if (value === \"\") {\n                            setSelectedRow(0);\n                            // setOpenEventModal(false)\n                            setCurrentEventType([]);\n                            setCurrentEvent(false);\n                            setCurrentStop(false);\n                        }\n                    },\n                    children: [\n                        currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.tripEvents.nodes.map((event, index)=>{\n                            var _event_eventType_PassengerDropFacility_type_replace_replace, _event_eventType_PassengerDropFacility_type_replace, _event_eventType_PassengerDropFacility_type, _event_eventType_PassengerDropFacility, _event_eventType_Tasking, _event_eventType_PassengerDropFacility1, _event_eventType_Tasking1, _event_eventType_Tasking2, _event_eventType_Tasking3, _event_eventType_RefuellingBunkering_fuelLog_nodes, _event_eventType_RefuellingBunkering_fuelLog, _event_eventType_RefuellingBunkering, _event_eventType_RefuellingBunkering_fuelLog1, _event_eventType_RefuellingBunkering1, _event_eventType_Tasking4, _event_eventType_Tasking5, _event_eventType_Tasking6, _event_eventType_Tasking7;\n                            // Generate event label and value outside the JSX\n                            const eventLabel = (event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" ? event === null || event === void 0 ? void 0 : (_event_eventType_PassengerDropFacility = event.eventType_PassengerDropFacility) === null || _event_eventType_PassengerDropFacility === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type = _event_eventType_PassengerDropFacility.type) === null || _event_eventType_PassengerDropFacility_type === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type_replace = _event_eventType_PassengerDropFacility_type.replace(/([a-z])([A-Z])/g, \"$1 $2\")) === null || _event_eventType_PassengerDropFacility_type_replace === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type_replace_replace = _event_eventType_PassengerDropFacility_type_replace.replace(\"Passenger Arrival\", \"Arrival\")) === null || _event_eventType_PassengerDropFacility_type_replace_replace === void 0 ? void 0 : _event_eventType_PassengerDropFacility_type_replace_replace.replace(\"Passenger Departure\", \"Departure\") : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" ? event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type.replace(/([a-z])([A-Z])/g, \"$1 $2\") : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"EventSupernumerary\" ? \"Supernumerary\" : event === null || event === void 0 ? void 0 : event.eventCategory.replace(/([a-z])([A-Z])/g, \"$1 $2\");\n                            const eventValue = (event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" ? event === null || event === void 0 ? void 0 : (_event_eventType_PassengerDropFacility1 = event.eventType_PassengerDropFacility) === null || _event_eventType_PassengerDropFacility1 === void 0 ? void 0 : _event_eventType_PassengerDropFacility1.type : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" ? event === null || event === void 0 ? void 0 : (_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.type : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Supernumerary\" ? \"EventSupernumerary\" : event === null || event === void 0 ? void 0 : event.eventCategory;\n                            // Generate event display text\n                            const eventDisplayText = getEventDisplayText(event);\n                            // Handle click on accordion item\n                            const handleAccordionItemClick = ()=>{\n                                // Toggle accordion state\n                                if (accordionValue === event.id.toString()) {\n                                    setAccordionValue(\"\");\n                                    setSelectedRow(0);\n                                    // setOpenEventModal(false)\n                                    setCurrentEventType([]);\n                                    setCurrentEvent(false);\n                                    setCurrentStop(false);\n                                } else {\n                                    setAccordionValue(event.id.toString());\n                                    setSelectedRow(event.id);\n                                    // setOpenEventModal(true)\n                                    setCurrentEventType({\n                                        label: eventLabel,\n                                        value: eventValue\n                                    });\n                                    setCurrentEvent(event);\n                                    setTripReport_Stops(false);\n                                    setDisplayDangerousGoodsPvpd(false);\n                                    setDisplayDangerousGoodsPvpdSailing(null);\n                                }\n                            };\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionItem, {\n                                value: event.id.toString(),\n                                className: (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.type) !== \"TaskingStartUnderway\" ? \"ml-[1.5rem]\" : \"\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionTrigger, {\n                                        onClick: handleAccordionItemClick,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center relative justify-between w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col inset-y-0 items-center justify-center absolute -left-[41px] sm:-left-[46px] w-5\",\n                                                    children: ((event === null || event === void 0 ? void 0 : event.eventCategory) !== \"Tasking\" || (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking3 = event.eventType_Tasking) === null || _event_eventType_Tasking3 === void 0 ? void 0 : _event_eventType_Tasking3.type) == \"TaskingStartUnderway\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_29__.cn)(\"size-[11px] z-10 rounded-full\", currentEvent.id === event.id ? \"border border-primary bg-curious-blue-200\" : currentEvent.eventCategory === event.eventCategory ? \"border border-primary bg-curious-blue-200\" : \"border border-cool-wedgewood-200 bg-outer-space-50\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                        lineNumber: 1000,\n                                                        columnNumber: 61\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 993,\n                                                    columnNumber: 53\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        eventDisplayText,\n                                                        (event === null || event === void 0 ? void 0 : event.eventCategory) === \"RefuellingBunkering\" && (event === null || event === void 0 ? void 0 : (_event_eventType_RefuellingBunkering = event.eventType_RefuellingBunkering) === null || _event_eventType_RefuellingBunkering === void 0 ? void 0 : (_event_eventType_RefuellingBunkering_fuelLog = _event_eventType_RefuellingBunkering.fuelLog) === null || _event_eventType_RefuellingBunkering_fuelLog === void 0 ? void 0 : (_event_eventType_RefuellingBunkering_fuelLog_nodes = _event_eventType_RefuellingBunkering_fuelLog.nodes) === null || _event_eventType_RefuellingBunkering_fuelLog_nodes === void 0 ? void 0 : _event_eventType_RefuellingBunkering_fuelLog_nodes.length) > 0 && getFuelTotals(event === null || event === void 0 ? void 0 : (_event_eventType_RefuellingBunkering1 = event.eventType_RefuellingBunkering) === null || _event_eventType_RefuellingBunkering1 === void 0 ? void 0 : (_event_eventType_RefuellingBunkering_fuelLog1 = _event_eventType_RefuellingBunkering1.fuelLog) === null || _event_eventType_RefuellingBunkering_fuelLog1 === void 0 ? void 0 : _event_eventType_RefuellingBunkering_fuelLog1.nodes)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1014,\n                                                    columnNumber: 53\n                                                }, this),\n                                                (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking4 = event.eventType_Tasking) === null || _event_eventType_Tasking4 === void 0 ? void 0 : _event_eventType_Tasking4.type) === \"TaskingStartUnderway\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking5 = event.eventType_Tasking) === null || _event_eventType_Tasking5 === void 0 ? void 0 : _event_eventType_Tasking5.status) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"\".concat((event === null || event === void 0 ? void 0 : (_event_eventType_Tasking6 = event.eventType_Tasking) === null || _event_eventType_Tasking6 === void 0 ? void 0 : _event_eventType_Tasking6.status) === \"Open\" ? \"text-bright-turquoise-600\" : \"\", \" pr-2\"),\n                                                    children: event === null || event === void 0 ? void 0 : (_event_eventType_Tasking7 = event.eventType_Tasking) === null || _event_eventType_Tasking7 === void 0 ? void 0 : _event_eventType_Tasking7.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1036,\n                                                    columnNumber: 61\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                            lineNumber: 992,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                        lineNumber: 988,\n                                        columnNumber: 45\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionContent, {\n                                        className: \"pb-4\",\n                                        children: currentEventType && currentEvent && currentEvent.id === event.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                currentEventType.value === \"VesselRescue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_vessel_rescue__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    offline: offline,\n                                                    geoLocations: geoLocations,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1055,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"HumanRescue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_person_rescue__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    offline: offline,\n                                                    geoLocations: geoLocations,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1085,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"RestrictedVisibility\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_restricted_visibility__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    logBookConfig: logBookConfig,\n                                                    locked: locked || !edit_tripActivity,\n                                                    members: crewMembers\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1115,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"BarCrossing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_bar_crossing__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    members: crewMembers,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    logBookConfig: logBookConfig,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1148,\n                                                    columnNumber: 65\n                                                }, this),\n                                                (currentEventType.value === \"PassengerArrival\" || currentEventType.value === \"PassengerDeparture\" || currentEventType.value === \"WaterTaxiService\" || currentEventType.value === \"ScheduledPassengerService\") && //TODO: update this form\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_passenger_drop_facility__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    offline: offline,\n                                                    geoLocations: geoLocations,\n                                                    inLogbook: true,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    type: currentEventType.value,\n                                                    logBookConfig: logBookConfig,\n                                                    previousDropEvent: previousDropEvent(currentEvent),\n                                                    vessel: vessel,\n                                                    locked: locked || !edit_tripActivity,\n                                                    fuelLogs: fuelLogs\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1188,\n                                                    columnNumber: 65\n                                                }, this),\n                                                (currentEventType.value === \"TaskingStartUnderway\" || currentEventType.value === \"TaskingOnScene\" || currentEventType.value === \"TaskingOnTow\" || currentEventType.value === \"TaskingPaused\" || currentEventType.value === \"TaskingResumed\" || currentEventType.value === \"TaskingComplete\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_tasking__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    offline: offline,\n                                                    geoLocations: geoLocations,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    type: currentEventType.value,\n                                                    logBookConfig: logBookConfig,\n                                                    previousDropEvent: mainTaskingEvent(currentEvent),\n                                                    vessel: vessel,\n                                                    members: crewMembers,\n                                                    locked: locked || !edit_tripActivity,\n                                                    fuelLogs: fuelLogs\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1244,\n                                                    columnNumber: 65\n                                                }, this),\n                                                permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__.hasPermission)(\"RECORD_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: currentEventType.value === \"CrewTraining\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_crew_training_event__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        offline: offline,\n                                                        vesselId: +vesselID,\n                                                        trainingTypeId: 0,\n                                                        currentTrip: currentTrip,\n                                                        updateTripReport: updateTripReport,\n                                                        selectedEvent: currentEvent,\n                                                        tripReport: tripReport,\n                                                        closeModal: handleSetCurrentEventType,\n                                                        crewMembers: crewMembers,\n                                                        masterID: masterID,\n                                                        logBookConfig: logBookConfig,\n                                                        vessels: vessels,\n                                                        locked: locked || !edit_tripActivity,\n                                                        logBookStartDate: logBookStartDate\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                        lineNumber: 1298,\n                                                        columnNumber: 77\n                                                    }, this)\n                                                }, void 0, false),\n                                                currentEventType.value === \"EventSupernumerary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_supernumerary_event__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    offline: offline,\n                                                    logBookConfig: logBookConfig,\n                                                    locked: locked || !edit_tripActivity,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    tripReport: tripReport,\n                                                    selectedEvent: currentEvent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1348,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"RefuellingBunkering\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_refuelling_bunkering__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    logBookConfig: logBookConfig,\n                                                    locked: locked || !edit_tripActivity,\n                                                    mainFuelLogs: fuelLogs\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1378,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"TripUpdate\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_trip_update__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1414,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"PilotTransfer\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_pilot_transfer__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1441,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"InfringementNotice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_InfringementNotices__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    crewMembers: crewMembers,\n                                                    locked: locked || !edit_tripActivity,\n                                                    visibility: // selectedRow ===\n                                                    //     event.id &&\n                                                    currentEventType && currentEvent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1468,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"IncidentRecord\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_incident_record_incident_record_form__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    id: (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.incidentRecordID) || 0,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    inLogbook: true,\n                                                    selectedEvent: currentEvent,\n                                                    offline: offline,\n                                                    tripReport: tripReport\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1504,\n                                                    columnNumber: 65\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                        lineNumber: 1047,\n                                        columnNumber: 45\n                                    }, this)\n                                ]\n                            }, index + \"_events\", true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                lineNumber: 976,\n                                columnNumber: 41\n                            }, this);\n                        }),\n                        currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops3 = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops3 === void 0 ? void 0 : _currentTrip_tripReport_Stops3.nodes.map((event, index)=>{\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionItem, {\n                                value: \"stop_\".concat(event.id),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionTrigger, {\n                                        onClick: handleStopAccordionItemClick(event.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: getStopDisplayText(event)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                lineNumber: 1552,\n                                                columnNumber: 53\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                            lineNumber: 1551,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                        lineNumber: 1547,\n                                        columnNumber: 45\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionContent, {\n                                        className: \"pb-4\",\n                                        children: currentEventType && currentStop && currentStop.id === event.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: currentEventType.value === \"PassengerVehiclePickDrop\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_passenger_vehicle_pick_drop__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                offline: offline,\n                                                geoLocations: geoLocations,\n                                                updateTripReport: updateTripReport,\n                                                currentTrip: currentTrip,\n                                                selectedEvent: currentStop,\n                                                tripReport: tripReport,\n                                                closeModal: handleSetCurrentEventType,\n                                                type: currentEventType.value,\n                                                logBookConfig: logBookConfig,\n                                                members: crewMembers,\n                                                locked: locked || !edit_tripActivity,\n                                                tripReport_Stops: tripReport_Stops,\n                                                setTripReport_Stops: setTripReport_Stops,\n                                                displayDangerousGoods: displayDangerousGoodsPvpd,\n                                                setDisplayDangerousGoods: setDisplayDangerousGoodsPvpd,\n                                                displayDangerousGoodsSailing: displayDangerousGoodsPvpdSailing,\n                                                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsPvpdSailing,\n                                                allPVPDDangerousGoods: allPVPDDangerousGoods,\n                                                setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                                                selectedDGR: selectedDGRPVPD,\n                                                setSelectedDGR: setSelectedDGRPVPD\n                                            }, \"pvpd-\".concat(event.id), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                lineNumber: 1567,\n                                                columnNumber: 65\n                                            }, this)\n                                        }, void 0, false)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                        lineNumber: 1559,\n                                        columnNumber: 45\n                                    }, this)\n                                ]\n                            }, index + \"_stops\", true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                lineNumber: 1544,\n                                columnNumber: 41\n                            }, this);\n                        })\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                    lineNumber: 878,\n                    columnNumber: 25\n                }, this)\n            }, void 0, false) : null,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-start gap-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                    position: \"left\",\n                    className: \"w-full\",\n                    label: \"Activity Type \",\n                    children: activityTypeOptions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_22__.Combobox, {\n                        id: \"task-assigned\",\n                        options: activityTypeOptions,\n                        value: currentEventType,\n                        onChange: handleEventChange,\n                        title: \"Activity Type\",\n                        placeholder: \"Activity Type\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                        lineNumber: 1653,\n                        columnNumber: 25\n                    }, this) : // Failsafe - in case the activity types are not loaded.\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"outline\",\n                            onClick: fetchActivityTypes,\n                            children: \"Refresh activity types\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1664,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                        lineNumber: 1663,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                    lineNumber: 1648,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                lineNumber: 1646,\n                columnNumber: 13\n            }, this),\n            currentEventType && !currentEvent && !currentStop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    currentEventType.value === \"VesselRescue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_vessel_rescue__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            locked: locked || !edit_tripActivity\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1698,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"HumanRescue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_person_rescue__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            locked: locked || !edit_tripActivity\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1712,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"RestrictedVisibility\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_restricted_visibility__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_tripActivity,\n                            members: crewMembers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1726,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"BarCrossing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_bar_crossing__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            members: crewMembers,\n                            closeModal: handleSetCurrentEventType,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_tripActivity\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1741,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    (currentEventType.value === \"PassengerArrival\" || currentEventType.value === \"PassengerDeparture\" || currentEventType.value === \"WaterTaxiService\" || currentEventType.value === \"ScheduledPassengerService\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_passenger_drop_facility__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            type: currentEventType.value,\n                            logBookConfig: logBookConfig,\n                            inLogbook: true,\n                            previousDropEvent: previousDropEvent(currentEvent),\n                            vessel: vessel,\n                            locked: locked || !edit_tripActivity,\n                            fuelLogs: fuelLogs\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1760,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    (currentEventType.value === \"TaskingStartUnderway\" || currentEventType.value === \"TaskingOnScene\" || currentEventType.value === \"TaskingOnTow\" || currentEventType.value === \"TaskingPaused\" || currentEventType.value === \"TaskingResumed\" || currentEventType.value === \"TaskingComplete\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_tasking__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            inLogbook: true,\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            type: currentEventType.value,\n                            logBookConfig: logBookConfig,\n                            previousDropEvent: mainTaskingEvent(currentEvent),\n                            vessel: vessel,\n                            members: crewMembers,\n                            locked: locked || !edit_tripActivity,\n                            fuelLogs: fuelLogs\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1787,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__.hasPermission)(\"RECORD_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: currentEventType.value === \"CrewTraining\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_crew_training_event__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                offline: offline,\n                                vesselId: +vesselID,\n                                trainingTypeId: 0,\n                                currentTrip: currentTrip,\n                                updateTripReport: updateTripReport,\n                                selectedEvent: currentEvent,\n                                tripReport: tripReport,\n                                closeModal: handleSetCurrentEventType,\n                                crewMembers: crewMembers,\n                                masterID: masterID,\n                                logBookConfig: logBookConfig,\n                                vessels: vessels,\n                                locked: locked || !edit_tripActivity,\n                                logBookStartDate: logBookStartDate\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                lineNumber: 1813,\n                                columnNumber: 41\n                            }, this)\n                        }, void 0, false)\n                    }, void 0, false),\n                    currentEventType.value === \"EventSupernumerary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_supernumerary_event__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            inLogbook: true,\n                            offline: offline,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_tripActivity,\n                            closeModal: handleSetCurrentEventType,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            tripReport: tripReport,\n                            selectedEvent: currentEvent\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1839,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"PassengerVehiclePickDrop\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_passenger_vehicle_pick_drop__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentStop,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            type: currentEventType.value,\n                            logBookConfig: logBookConfig,\n                            members: crewMembers,\n                            locked: locked || !edit_tripActivity,\n                            tripReport_Stops: tripReport_Stops,\n                            setTripReport_Stops: setTripReport_Stops,\n                            displayDangerousGoods: displayDangerousGoodsPvpd,\n                            setDisplayDangerousGoods: setDisplayDangerousGoodsPvpd,\n                            displayDangerousGoodsSailing: displayDangerousGoodsPvpdSailing,\n                            setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsPvpdSailing,\n                            allPVPDDangerousGoods: allPVPDDangerousGoods,\n                            setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                            selectedDGR: selectedDGRPVPD,\n                            setSelectedDGR: setSelectedDGRPVPD\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1854,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"RefuellingBunkering\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_refuelling_bunkering__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_tripActivity,\n                            mainFuelLogs: fuelLogs\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1891,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"TripUpdate\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_trip_update__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            locked: locked || !edit_tripActivity\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1906,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"InfringementNotice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_InfringementNotices__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            crewMembers: crewMembers,\n                            locked: locked || !edit_tripActivity,\n                            visibility: currentEventType && !currentEvent && !currentStop\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1933,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"IncidentRecord\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_incident_record_incident_record_form__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                            id: (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.incidentRecordID) || 0,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            closeModal: handleSetCurrentEventType,\n                            inLogbook: true,\n                            selectedEvent: currentEvent,\n                            offline: offline,\n                            tripReport: tripReport\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1952,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false)\n                ]\n            }, void 0, true),\n            currentTrip.tripReportScheduleID > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radio_logs_schedule__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                open: displayRadioLogs,\n                setOpen: setDisplayRadioLogs,\n                currentTrip: currentTrip\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                lineNumber: 1967,\n                columnNumber: 17\n            }, this)\n        ]\n    }, lodash_uniqueId__WEBPACK_IMPORTED_MODULE_14___default()(), true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n        lineNumber: 861,\n        columnNumber: 9\n    }, this);\n}\n_s(Events, \"3rrNMu4zV6K2k4YQIASNWHgDV2c=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useSearchParams\n    ];\n});\n_c = Events;\nvar _c;\n$RefreshReg$(_c, \"Events\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/events.tsx\n"));

/***/ })

});