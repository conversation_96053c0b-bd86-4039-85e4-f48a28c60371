"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/vessel/info/page",{

/***/ "(app-pages-browser)/./src/app/ui/editor.tsx":
/*!*******************************!*\
  !*** ./src/app/ui/editor.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Editor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var react_quill_dist_quill_snow_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-quill/dist/quill.snow.css */ \"(app-pages-browser)/./node_modules/.pnpm/react-quill@2.0.0_react-dom_e84ee3d05de24e5925e3d303aba3a8e9/node_modules/react-quill/dist/quill.snow.css\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst QuillEditor = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_pnpm_react-quill_2_0_0_react-dom_e84ee3d05de24e5925e3d303aba3-5c8f62\").then(__webpack_require__.t.bind(__webpack_require__, /*! react-quill */ \"(app-pages-browser)/./node_modules/.pnpm/react-quill@2.0.0_react-dom_e84ee3d05de24e5925e3d303aba3a8e9/node_modules/react-quill/lib/index.js\", 23)), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\ui\\\\editor.tsx -> \" + \"react-quill\"\n        ]\n    },\n    ssr: false\n});\n_c = QuillEditor;\nfunction Editor(param) {\n    let { content, placeholder, handleEditorChange, handleEditorBlur, className, id, disabled, name } = param;\n    _s();\n    // Use existing breakpoints hook from the codebase\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_5__.useBreakpoints)();\n    const isMobile = !bp[\"tablet-md\"] // tablet-md is 768px, so this matches the original logic\n    ;\n    const desktopToolbar = [\n        [\n            {\n                header: [\n                    1,\n                    2,\n                    3,\n                    false\n                ]\n            }\n        ],\n        [\n            \"bold\",\n            \"italic\",\n            \"underline\"\n        ],\n        [\n            {\n                list: \"ordered\"\n            },\n            {\n                list: \"bullet\"\n            }\n        ],\n        [\n            \"link\",\n            \"image\"\n        ],\n        [\n            {\n                align: []\n            },\n            {\n                color: []\n            }\n        ],\n        [\n            \"clean\"\n        ]\n    ];\n    // Enhanced mobile toolbar with more options than before\n    const mobileToolbar = [\n        [\n            {\n                header: [\n                    1,\n                    2,\n                    false\n                ]\n            }\n        ],\n        [\n            \"bold\",\n            \"italic\",\n            \"underline\"\n        ],\n        [\n            {\n                list: \"ordered\"\n            },\n            {\n                list: \"bullet\"\n            }\n        ],\n        [\n            \"link\"\n        ],\n        [\n            \"clean\"\n        ]\n    ];\n    // Use useMemo to prevent unnecessary re-renders and dynamic toolbar switching\n    const quillModules = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            toolbar: isMobile ? mobileToolbar : desktopToolbar\n        }), [\n        isMobile\n    ]);\n    const quillFormats = [\n        \"header\",\n        \"bold\",\n        \"italic\",\n        \"underline\",\n        \"strike\",\n        \"blockquote\",\n        \"list\",\n        \"bullet\",\n        \"link\",\n        \"image\",\n        \"align\",\n        \"color\",\n        \"code-block\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuillEditor, {\n        value: content,\n        placeholder: placeholder,\n        onChange: handleEditorChange,\n        onBlur: handleEditorBlur,\n        modules: quillModules,\n        formats: quillFormats,\n        readOnly: disabled,\n        id: id,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"w-full min-h-60 mb-2 bg-card border text-input border-border rounded-lg\", // Responsive improvements\n        \"tablet-md:min-h-40\", \"[&_.ql-toolbar]:flex-wrap\", \"[&_.ql-toolbar]:gap-1\", \"[&_.ql-editor]:text-sm tablet-md:[&_.ql-editor]:text-base\", \"[&_.ql-toolbar_.ql-formats]:flex-wrap\", \"[&_.ql-toolbar_.ql-formats]:gap-1\", // Better mobile touch targets\n        \"[&_.ql-toolbar_button]:min-h-[44px] [&_.ql-toolbar_button]:min-w-[44px]\", \"[&_.ql-toolbar_.ql-picker]:min-h-[44px]\", className)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\editor.tsx\",\n        lineNumber: 78,\n        columnNumber: 9\n    }, this);\n}\n_s(Editor, \"ljpbraJbyBcrRHiP7JzyubTY4eA=\", false, function() {\n    return [\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_5__.useBreakpoints\n    ];\n});\n_c1 = Editor;\nvar _c, _c1;\n$RefreshReg$(_c, \"QuillEditor\");\n$RefreshReg$(_c1, \"Editor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/editor.tsx\n"));

/***/ })

});