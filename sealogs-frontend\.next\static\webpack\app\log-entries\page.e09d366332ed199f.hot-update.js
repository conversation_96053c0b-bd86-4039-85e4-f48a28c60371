"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/trip-log.tsx":
/*!*****************************************!*\
  !*** ./src/app/ui/logbook/trip-log.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TripLog; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lodash/uniqueId */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/uniqueId.js\");\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _app_offline_models_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/client */ \"(app-pages-browser)/./src/app/offline/models/client.js\");\n/* harmony import */ var _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_offline_models_eventType__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/models/eventType */ \"(app-pages-browser)/./src/app/offline/models/eventType.js\");\n/* harmony import */ var _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/offline/models/tripReport_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/tripReport_LogBookEntrySection.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_accordion__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/accordion */ \"(app-pages-browser)/./src/components/ui/accordion.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _components_signature_pad__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/signature-pad */ \"(app-pages-browser)/./src/components/signature-pad.tsx\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _depart_time__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./depart-time */ \"(app-pages-browser)/./src/app/ui/logbook/depart-time.tsx\");\n/* harmony import */ var _exp_arrival_time__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./exp-arrival-time */ \"(app-pages-browser)/./src/app/ui/logbook/exp-arrival-time.tsx\");\n/* harmony import */ var _actual_arrival_time__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./actual-arrival-time */ \"(app-pages-browser)/./src/app/ui/logbook/actual-arrival-time.tsx\");\n/* harmony import */ var _events__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./events */ \"(app-pages-browser)/./src/app/ui/logbook/events.tsx\");\n/* harmony import */ var _trip_log_pob__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./trip-log-pob */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-pob.tsx\");\n/* harmony import */ var _trip_log_vob__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! ./trip-log-vob */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-vob.tsx\");\n/* harmony import */ var _trip_log_dgr__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./trip-log-dgr */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log-dgr.tsx\");\n/* harmony import */ var _components_trip_comments__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! ./components/trip-comments */ \"(app-pages-browser)/./src/app/ui/logbook/components/trip-comments.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_master__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! ./components/master */ \"(app-pages-browser)/./src/app/ui/logbook/components/master.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n// React and Next.js imports\n\n\n\n\n\n// Utility imports\n\n\n\n\n\n\n// Model imports\n\n\n\n\n\n// UI Component imports\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Reusable TripReportAccordionContent component\nconst TripReportAccordionContent = (param)=>{\n    let { currentTrip, offline, tripReport, updateTripReport, updateTripReport_LogBookEntrySection, currentTripRef, locations, locked, edit_tripReport, vessel, crewMembers, logBookConfig, client, canCarryVehicles, canCarryDangerousGoods, selectedDGR, displayDangerousGoods, setDisplayDangerousGoods, displayDangerousGoodsSailing, setDisplayDangerousGoodsSailing, allDangerousGoods, setAllDangerousGoods, logBookStartDate, masterID, vessels, setSelectedRowEvent, setCurrentEventTypeEvent, setCurrentStopEvent, currentEventTypeEvent, currentStopEvent, tripReport_Stops, setTripReport_Stops, displayDangerousGoodsPvpd, setDisplayDangerousGoodsPvpd, displayDangerousGoodsPvpdSailing, setDisplayDangerousGoodsPvpdSailing, allPVPDDangerousGoods, setAllPVPDDangerousGoods, selectedDGRPVPD, setSelectedDGRPVPD, fuelLogs, comment, setComment, displayFieldTripLog, signatureKey, signature, setSignature, handleCancel, handleSave } = param;\n    var _currentTripRef_current;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_34__.cn)(\"space-y-6\", locked || !edit_tripReport ? \"opacity-70 pointer-events-none\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col inset-y-0 items-center pt-10 pb-4 absolute -left-5 sm:-left-[25px] min-h-24 w-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_34__.cn)(\"size-[11px] z-10 rounded-full\", \"border border-neutral-400 bg-background\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_34__.cn)(\"border-l h-full border-wedgewood-200 border-dashed\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_34__.cn)(\"size-[11px] z-10 rounded-full\", \"border border-neutral-400 bg-background\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_depart_time__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                        offline: offline,\n                        currentTrip: currentTrip,\n                        tripReport: tripReport,\n                        templateStyle: \"\",\n                        updateTripReport: updateTripReport\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_21__.Label, {\n                        label: \"Departure location\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                            offline: offline,\n                            setCurrentLocation: (location)=>{\n                            // Store coordinates if needed for direct coordinate input\n                            },\n                            handleLocationChange: (selectedLocation)=>{\n                                // Update the from location\n                                if (offline) {\n                                    updateTripReport({\n                                        id: [\n                                            ...tripReport.map((trip)=>trip.id),\n                                            currentTrip.id\n                                        ],\n                                        currentTripID: currentTrip.id,\n                                        key: \"fromLocationID\",\n                                        value: selectedLocation.value,\n                                        label: selectedLocation.label\n                                    });\n                                } else {\n                                    var _currentTripRef_current;\n                                    // For online mode, use the mutation\n                                    updateTripReport_LogBookEntrySection({\n                                        variables: {\n                                            input: {\n                                                id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                                                fromLocationID: selectedLocation.value\n                                            }\n                                        }\n                                    });\n                                }\n                            },\n                            currentEvent: {\n                                geoLocationID: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLocationID) || 0,\n                                lat: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLat) || 0,\n                                long: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.fromLong) || 0\n                            },\n                            showAddNewLocation: true,\n                            showUseCoordinates: true,\n                            showCurrentLocation: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_20__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_17__.H5, {\n                        children: \"PEOPLE ON BOARD\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_pob__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                        offline: offline,\n                        currentTrip: currentTrip,\n                        tripReport: tripReport,\n                        vessel: vessel,\n                        crewMembers: crewMembers,\n                        logBookConfig: logBookConfig,\n                        masterTerm: client === null || client === void 0 ? void 0 : client.masterTerm,\n                        updateTripReport: updateTripReport,\n                        setTab: setTab\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_20__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            canCarryVehicles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_vob__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                offline: offline,\n                                currentTrip: currentTrip,\n                                logBookConfig: logBookConfig\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 25\n                            }, undefined),\n                            canCarryDangerousGoods && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log_dgr__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                offline: offline,\n                                locked: locked || !edit_tripReport,\n                                currentTrip: currentTrip,\n                                logBookConfig: logBookConfig,\n                                selectedDGR: selectedDGR,\n                                members: crewMembers,\n                                displayDangerousGoods: displayDangerousGoods,\n                                setDisplayDangerousGoods: setDisplayDangerousGoods,\n                                displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                                allDangerousGoods: allDangerousGoods,\n                                setAllDangerousGoods: setAllDangerousGoods\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_20__.Separator, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_events__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                        offline: offline,\n                        logBookStartDate: logBookStartDate,\n                        currentTrip: currentTrip,\n                        logBookConfig: logBookConfig,\n                        updateTripReport: updateTripReport,\n                        locked: locked,\n                        geoLocations: locations,\n                        tripReport: tripReport,\n                        crewMembers: crewMembers,\n                        masterID: masterID,\n                        vessel: vessel,\n                        vessels: vessels,\n                        setSelectedRow: setSelectedRowEvent,\n                        setCurrentEventType: setCurrentEventTypeEvent,\n                        setCurrentStop: setCurrentStopEvent,\n                        currentEventType: currentEventTypeEvent,\n                        currentStop: currentStopEvent,\n                        tripReport_Stops: tripReport_Stops,\n                        setTripReport_Stops: setTripReport_Stops,\n                        displayDangerousGoodsPvpd: displayDangerousGoodsPvpd,\n                        setDisplayDangerousGoodsPvpd: setDisplayDangerousGoodsPvpd,\n                        displayDangerousGoodsPvpdSailing: displayDangerousGoodsPvpdSailing,\n                        setDisplayDangerousGoodsPvpdSailing: setDisplayDangerousGoodsPvpdSailing,\n                        allPVPDDangerousGoods: allPVPDDangerousGoods,\n                        setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                        selectedDGRPVPD: selectedDGRPVPD,\n                        setSelectedDGRPVPD: setSelectedDGRPVPD,\n                        fuelLogs: fuelLogs\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_20__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_21__.Label, {\n                        label: \"Arrival location\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                            offline: offline,\n                            setCurrentLocation: (location)=>{\n                            // Store coordinates if needed for direct coordinate input\n                            },\n                            handleLocationChange: (selectedLoc)=>{\n                                // Update the to location\n                                if (offline) {\n                                    updateTripReport({\n                                        id: [\n                                            ...tripReport.map((trip)=>trip.id),\n                                            currentTrip.id\n                                        ],\n                                        currentTripID: currentTrip.id,\n                                        key: \"toLocationID\",\n                                        value: selectedLoc.value,\n                                        label: selectedLoc.label\n                                    });\n                                } else {\n                                    var _currentTripRef_current;\n                                    // For online mode, use the mutation\n                                    updateTripReport_LogBookEntrySection({\n                                        variables: {\n                                            input: {\n                                                id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                                                toLocationID: selectedLoc.value\n                                            }\n                                        }\n                                    });\n                                }\n                            },\n                            currentEvent: {\n                                geoLocationID: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLocationID) || 0,\n                                lat: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLat) || 0,\n                                long: (currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.toLong) || 0\n                            },\n                            showAddNewLocation: true,\n                            showUseCoordinates: true,\n                            showCurrentLocation: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 129,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_exp_arrival_time__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                offline: offline,\n                currentTrip: currentTrip,\n                tripReport: tripReport,\n                updateTripReport: updateTripReport\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 339,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_actual_arrival_time__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                offline: offline,\n                currentTrip: currentTrip,\n                tripReport: tripReport,\n                updateTripReport: updateTripReport\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 346,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_trip_comments__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                offline: offline,\n                currentTrip: currentTrip,\n                tripReport: tripReport,\n                setCommentField: setComment,\n                updateTripReport: updateTripReport\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 353,\n                columnNumber: 13\n            }, undefined),\n            displayFieldTripLog(\"MasterID\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked || !edit_tripReport ? \"pointer-events-none\" : \"\", \" max-w-sm\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_master__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                    offline: offline,\n                    currentTrip: currentTrip,\n                    tripReport: tripReport,\n                    crewMembers: crewMembers,\n                    updateTripReport: updateTripReport\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 361,\n                columnNumber: 17\n            }, undefined),\n            displayFieldTripLog(\"Signature\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_signature_pad__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                locked: locked,\n                title: \"Signature Confirmation\",\n                description: \"By signing below, I confirm that the recorded entries are accurate to the best of my knowledge and in accordance with the vessel's operating procedures and regulations.\",\n                signature: currentTripRef === null || currentTripRef === void 0 ? void 0 : (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.sectionSignature,\n                onSignatureChanged: (sign)=>{\n                    setSignature(sign);\n                }\n            }, \"\".concat(signatureKey, \"-\").concat(currentTrip.id), false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 374,\n                columnNumber: 17\n            }, undefined),\n            !locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_35__.FormFooter, {\n                className: \"justify-end gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"back\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"],\n                        onClick: handleCancel,\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"],\n                        onClick: handleSave,\n                        children: \"Update\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 387,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n        lineNumber: 122,\n        columnNumber: 9\n    }, undefined);\n};\n_c = TripReportAccordionContent;\nfunction TripLog(param) {\n    let { tripReport = false, logBookConfig, updateTripReport, locked, crewMembers, masterID, createdTab = false, setCreatedTab, currentTrip = false, setCurrentTrip, vessels, offline = false, fuelLogs, logBookStartDate, setTab: setTab1 } = param;\n    var _tripReport_find;\n    _s();\n    // const router = useRouter()\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = (_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    var _searchParams_get1;\n    const vesselID = (_searchParams_get1 = searchParams.get(\"vesselID\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : 0;\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_16__.useToast)();\n    // Tab state is managed by parent component\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [eventTypes, setEventTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openTripModal, setOpenTripModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [bufferTripID, setBufferTripID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [vessel, setVessel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [accordionValue, setAccordionValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedDGR, setSelectedDGR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [openRiskAnalysis, setOpenTripStartRiskAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoods, setDisplayDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsSailing, setDisplayDangerousGoodsSailing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsPvpd, setDisplayDangerousGoodsPvpd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [displayDangerousGoodsPvpdSailing, setDisplayDangerousGoodsPvpdSailing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // const [openEventModal, setOpenEventModal] = useState(false)\n    // const [selectedRowdgr, setSelectedRowdgr] = useState<any>(false)\n    const [tripReport_Stops, setTripReport_Stops] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedDGRPVPD, setSelectedDGRPVPD] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [allPVPDDangerousGoods, setAllPVPDDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRowEvent, setSelectedRowEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [riskBufferEvDgr, setRiskBufferEvDgr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allDangerousGoods, setAllDangerousGoods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentEventTypeEvent, setCurrentEventTypeEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStopEvent, setCurrentStopEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [client, setClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [signature, setSignature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [signatureKey, setSignatureKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7___default()());\n    const currentTripRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [deleteTripItem, setDeleteTripItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deleteTripConfirmation, setDeleteTripConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const canCarryDangerousGoods = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _vessel_vesselSpecifics;\n        return !!(vessel === null || vessel === void 0 ? void 0 : (_vessel_vesselSpecifics = vessel.vesselSpecifics) === null || _vessel_vesselSpecifics === void 0 ? void 0 : _vessel_vesselSpecifics.carriesDangerousGoods);\n    }, [\n        vessel\n    ]);\n    const canCarryVehicles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _vessel_vesselSpecifics;\n        return !!(vessel === null || vessel === void 0 ? void 0 : (_vessel_vesselSpecifics = vessel.vesselSpecifics) === null || _vessel_vesselSpecifics === void 0 ? void 0 : _vessel_vesselSpecifics.carriesVehicles);\n    }, [\n        vessel\n    ]);\n    // Initialize client\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_9__.getOneClient)(setClient);\n    }\n    // Update signature state when currentTrip changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentTrip && currentTrip.sectionSignature) {\n            setSignature(currentTrip.sectionSignature.signatureData || \"\");\n        } else {\n            setSignature(\"\");\n        }\n    }, [\n        currentTrip\n    ]);\n    const [comment, setComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(tripReport ? tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find === void 0 ? void 0 : _tripReport_find.comment : \"\");\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_tripReport, setEdit_tripReport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const clientModel = new _app_offline_models_client__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\n    const vesselModel = new _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\n    const geoLocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n    const eventTypeModel = new _app_offline_models_eventType__WEBPACK_IMPORTED_MODULE_14__[\"default\"]();\n    const tripReportModel = new _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_15__[\"default\"]();\n    const [openTripSelectionDialog, setOpenTripSelectionDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tripReportSchedules, setTripReportSchedules] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTripReportSchedule, setSelectedTripReportSchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tripScheduleServices, setTripScheduleServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTripScheduleServiceID, setSelectedTripScheduleServiceID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showNextTrips, setShowNextTrips] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreatingScheduledTrip, setIsCreatingScheduledTrip] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.hasPermission)(\"EDIT_LOGBOOKENTRY_TRIPREPORT\", permissions)) {\n                setEdit_tripReport(true);\n            } else {\n                setEdit_tripReport(false);\n            }\n        }\n    };\n    const offlineLoad = async ()=>{\n        const locations = await geoLocationModel.getAll();\n        setLocations(locations);\n        const types = await eventTypeModel.getAll();\n        setEventTypes(types);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_8__.getPermissions);\n        loadTripScheduleServices();\n        if (!locations) {\n            if (offline) {\n                offlineLoad();\n            } else {\n                loadLocations();\n                loadEventTypes();\n            }\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (createdTab) {\n            setSelectedTab(createdTab);\n        }\n    }, [\n        createdTab\n    ]);\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_9__.getVesselByID)(+vesselID, setVessel);\n    }\n    const scrollToAccordionItem = (tripId)=>{\n        const element = document.getElementById(\"triplog-\".concat(tripId));\n        if (element) {\n            element.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (tripReport && currentTrip) {\n            const trip = tripReport.find((trip)=>trip.id === currentTrip.id);\n            currentTripRef.current = trip;\n            setCurrentTrip(trip);\n        }\n        if (tripReport && bufferTripID > 0) {\n            const trip = tripReport.find((trip)=>trip.id === bufferTripID);\n            if (trip) {\n                currentTripRef.current = trip;\n                setCurrentTrip(trip);\n                // Only expand accordion and scroll for regular trips, not scheduled trips\n                if (!selectedTripReportSchedule) {\n                    setAccordionValue(trip.id.toString());\n                    scrollToAccordionItem(trip.id);\n                }\n                setOpenTripModal(true);\n                setSelectedTab(trip.id);\n                setSignatureKey(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7___default()());\n                // Initialize signature data if available\n                if (trip.sectionSignature) {\n                    setSignature(trip.sectionSignature.signatureData || \"\");\n                } else {\n                    setSignature(\"\");\n                }\n                // Initialize trip-specific state\n                setRiskBufferEvDgr(trip === null || trip === void 0 ? void 0 : trip.dangerousGoodsChecklist);\n                setOpenTripStartRiskAnalysis(false);\n                setAllDangerousGoods(false);\n                setCurrentStopEvent(false);\n                setCurrentEventTypeEvent(false);\n                setSelectedRowEvent(false);\n                setDisplayDangerousGoods((trip === null || trip === void 0 ? void 0 : trip.enableDGR) === true);\n                setDisplayDangerousGoodsSailing((trip === null || trip === void 0 ? void 0 : trip.designatedDangerousGoodsSailing) === true);\n                setDisplayDangerousGoodsPvpd(false);\n                setDisplayDangerousGoodsPvpdSailing(null);\n                setAllPVPDDangerousGoods(false);\n                setSelectedDGRPVPD(false);\n                setTripReport_Stops(false);\n            }\n            setBufferTripID(0);\n        }\n    }, [\n        tripReport\n    ]);\n    const [loadLocations] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_39__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GET_GEO_LOCATIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            setLocations(response.readGeoLocations.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading locations\", error);\n        }\n    });\n    const [loadEventTypes] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_39__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GET_EVENT_TYPES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            setEventTypes(response.readEventTypes.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading activity types\", error);\n        }\n    });\n    const [createTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_40__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateTripReport_Stop, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"Error creating passenger drop facility\", error);\n        }\n    });\n    const handleCreateTripReportScheduleStops = async (logBookEntrySectionID)=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(selectedTripReportSchedule)) {\n            const tripStops = selectedTripReportSchedule.tripReportScheduleStops.nodes || [];\n            await Promise.all(tripStops.map(async (stop)=>{\n                const input = {\n                    logBookEntrySectionID: logBookEntrySectionID,\n                    tripReportScheduleStopID: stop.id,\n                    arriveTime: stop.arriveTime,\n                    departTime: stop.departTime,\n                    stopLocationID: stop.stopLocationID\n                };\n                await createTripReport_Stop({\n                    variables: {\n                        input: input\n                    }\n                });\n            }));\n            setSelectedTripReportSchedule(null);\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        logBookEntrySectionID\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        logBookEntrySectionID\n                    ]\n                });\n            }\n        }\n    };\n    const [createTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_40__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateTripReport_LogBookEntrySection, {\n        onCompleted: (response)=>{\n            const data = response.createTripReport_LogBookEntrySection;\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ]\n                });\n            }\n            setCreatedTab(data.id);\n            setBufferTripID(data.id);\n            handleCreateTripReportScheduleStops(data.id);\n            // Always set accordion value and scroll for newly created trips\n            setAccordionValue(data.id.toString());\n            scrollToAccordionItem(data.id);\n            // Reset the flag\n            setIsCreatingScheduledTrip(false);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating trip report\", error);\n        }\n    });\n    const [updateTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_40__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateTripReport_LogBookEntrySection, {\n        onCompleted: (response)=>{\n            const data = response.updateTripReport_LogBookEntrySection;\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            }\n            setBufferTripID(data.id);\n            setCurrentTrip(false);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating trip report\", error);\n        }\n    });\n    const [readTripReportSchedules, { loading: readTripReportSchedulesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_39__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.ReadTripReportSchedules, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTripReportSchedules.nodes.filter((trip)=>// only show trips for the current vessel\n                trip.vehicles.nodes.some((vehicle)=>+vehicle.id === +vesselID));\n            if (showNextTrips) {\n                // only show 1 past trip and 4 upcoming trips\n                const currentTime = dayjs__WEBPACK_IMPORTED_MODULE_5___default()().format(\"HH:mm:ss\");\n                const pastIndex = data.findIndex((trip)=>trip.departTime >= currentTime);\n                const result = (pastIndex > 0 ? [\n                    data[pastIndex - 1]\n                ] : []).concat(data.slice(pastIndex, pastIndex + 4));\n                setTripReportSchedules(result);\n            } else {\n                setTripReportSchedules(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error loading TripReportSchedules\", error);\n        }\n    });\n    const [readTripScheduleServices] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_39__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.ReadTripScheduleServices, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTripScheduleServices.nodes.map((tss)=>{\n                return {\n                    label: tss.title,\n                    value: tss.id\n                };\n            });\n            setTripScheduleServices(data);\n            setTripReportSchedules([]);\n        // setOpenTripSelectionDialog(true)\n        },\n        onError: (error)=>{\n            console.error(\"Error loading TripScheduleServices\", error);\n        }\n    });\n    const loadTripScheduleServices = async ()=>{\n        await readTripScheduleServices({\n            variables: {\n                filter: {\n                    vehicles: {\n                        id: {\n                            eq: vesselID\n                        }\n                    }\n                }\n            }\n        });\n    };\n    const loadTripReportSchedules = async (tripScheduleServiceID)=>{\n        setTripReportSchedules([]);\n        await readTripReportSchedules({\n            variables: {\n                filter: {\n                    // archived: { eq: false },\n                    // start: { eq: logBookStartDate },\n                    tripScheduleServiceID: {\n                        eq: tripScheduleServiceID\n                    }\n                }\n            }\n        });\n    };\n    const doCreateTripReport = async function(input) {\n        let isScheduledTrip = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!edit_tripReport) {\n            toast({\n                title: \"Error\",\n                description: \"You do not have permission to add a trip\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setOpenTripModal(false);\n        setCurrentTrip(false);\n        setIsCreatingScheduledTrip(isScheduledTrip);\n        if (offline) {\n            const data = await tripReportModel.save({\n                ...input,\n                id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__.generateUniqueId)()\n            });\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ]\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ]\n                });\n            }\n            setCreatedTab(data.id);\n            setBufferTripID(data.id);\n            // For offline mode, immediately set accordion value since the trip is created synchronously\n            setAccordionValue(data.id.toString());\n            // Reset the flag\n            setIsCreatingScheduledTrip(false);\n        } else {\n            createTripReport_LogBookEntrySection({\n                variables: {\n                    input: input\n                }\n            });\n        }\n        setRiskBufferEvDgr(false);\n        setOpenTripStartRiskAnalysis(false);\n        setAllDangerousGoods(false);\n        setCurrentStopEvent(false);\n        setCurrentEventTypeEvent(false);\n        setSelectedRowEvent(false);\n        setDisplayDangerousGoods(false);\n        setDisplayDangerousGoodsSailing(false);\n        setDisplayDangerousGoodsPvpd(false);\n        setDisplayDangerousGoodsPvpdSailing(null);\n        setAllPVPDDangerousGoods(false);\n        setSelectedDGRPVPD(false);\n        setTripReport_Stops(false);\n    // setSelectedTripScheduleServiceID(null)\n    // setTripReportSchedules([])\n    // setShowNextTrips(false)\n    };\n    const handleAddTrip = async ()=>{\n        const allowedVesselTypes = [\n            \"SLALL\",\n            \"Tug_Boat\",\n            \"Passenger_Ferry\",\n            \"Water_Taxi\"\n        ];\n        if (allowedVesselTypes.includes(vessel.vesselType)) {\n            loadTripScheduleServices();\n        } else {\n            handleCustomTrip();\n        }\n    };\n    const handleCustomTrip = ()=>{\n        setOpenTripSelectionDialog(false);\n        const input = {\n            logBookEntryID: logentryID\n        };\n        doCreateTripReport(input, false) // false indicates this is a regular trip\n        ;\n    };\n    // Removed unused handleEditTrip function\n    const [createLogBookEntrySection_Signature] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_40__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateLogBookEntrySection_Signature, {\n        onCompleted: (response)=>{\n            const data = response.createLogBookEntrySection_Signature;\n            updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: +data.logBookEntrySectionID,\n                        sectionSignatureID: +(data === null || data === void 0 ? void 0 : data.id)\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error saving signature\", error);\n        }\n    });\n    const [updateLogBookEntrySection_Signature] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_40__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateLogBookEntrySection_Signature, {\n        onCompleted: (response)=>{\n            var _currentTripRef_current;\n            const data = response.updateLogBookEntrySection_Signature;\n            updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                        sectionSignatureID: +(data === null || data === void 0 ? void 0 : data.id)\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"341 TripLog updateLogBookEntrySection_Signature\", error);\n        }\n    });\n    const handleSave = async ()=>{\n        // Log signature information for debugging\n        // Ensure we have a valid signature\n        const sigVariables = {\n            logBookEntrySectionID: currentTrip.id,\n            memberID: localStorage.getItem(\"userId\"),\n            signatureData: signature || \"\"\n        };\n        if (+currentTrip.sectionSignatureID > 0) {\n            // Update signature\n            updateLogBookEntrySection_Signature({\n                variables: {\n                    input: {\n                        ...sigVariables,\n                        id: +currentTrip.sectionSignatureID\n                    }\n                }\n            });\n        } else {\n            // Create signature\n            createLogBookEntrySection_Signature({\n                variables: {\n                    input: sigVariables !== null && sigVariables !== void 0 ? sigVariables : \"\"\n                }\n            });\n        }\n        if (offline) {\n            // updateTripReport_LogBookEntrySection\n            const data = await tripReportModel.save({\n                id: currentTrip.id,\n                comment: comment || null\n            });\n            if (tripReport) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            } else {\n                updateTripReport({\n                    id: [\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            }\n            setBufferTripID(data.id);\n        } else {\n            var _currentTripRef_current;\n            await updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: (_currentTripRef_current = currentTripRef.current) === null || _currentTripRef_current === void 0 ? void 0 : _currentTripRef_current.id,\n                        comment: comment || null\n                    }\n                }\n            });\n            setOpenTripModal(false);\n            setCurrentTrip(false);\n        }\n    };\n    const displayFieldTripLog = (fieldName)=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents, _dailyChecks__customisedComponentFields, _dailyChecks_;\n        const dailyChecks = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"TripReport_LogBookComponent\");\n        if ((dailyChecks === null || dailyChecks === void 0 ? void 0 : dailyChecks.length) > 0 && ((_dailyChecks_ = dailyChecks[0]) === null || _dailyChecks_ === void 0 ? void 0 : (_dailyChecks__customisedComponentFields = _dailyChecks_.customisedComponentFields) === null || _dailyChecks__customisedComponentFields === void 0 ? void 0 : _dailyChecks__customisedComponentFields.nodes.filter((field)=>field.fieldName === fieldName && field.status !== \"Off\").length) > 0) {\n            return true;\n        }\n        return false;\n    };\n    const convertTimeFormat = (time)=>{\n        if (time === null || time === undefined) return \"\";\n        const [hours, minutes] = time.split(\":\");\n        return \"\".concat(hours, \":\").concat(minutes);\n    };\n    const handleCancel = ()=>{\n        setOpenTripModal(false);\n        setCurrentTrip(false);\n        setAccordionValue(\"\");\n    };\n    // Removed unused functions\n    const initOffline = async ()=>{\n        var _localStorage_getItem;\n        // getOneClient\n        const client = await clientModel.getById((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0);\n        setClient(client);\n        // getVesselByID(+vesselID, setVessel)\n        const vessel = await vesselModel.getById(vesselID);\n        setVessel(vessel);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            initOffline();\n        }\n    }, [\n        offline\n    ]);\n    // Create combined trip data that merges tripReport and tripReportSchedules\n    const combinedTripData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const existingTrips = (tripReport || []).filter((trip)=>!(trip === null || trip === void 0 ? void 0 : trip.archived)).map((trip)=>{\n            var _trip_fromLocation, _trip_fromLocation1, _trip_toLocation, _trip_toLocation1, _trip_fromLocation2, _trip_toLocation2;\n            return {\n                ...trip,\n                isScheduled: false,\n                isCreated: true,\n                sortTime: trip.departTime || \"00:00:00\",\n                displayText: \"\".concat((trip === null || trip === void 0 ? void 0 : trip.departTime) ? convertTimeFormat(trip === null || trip === void 0 ? void 0 : trip.departTime) + \" - \" : \"No depart time - \").concat((trip === null || trip === void 0 ? void 0 : (_trip_fromLocation = trip.fromLocation) === null || _trip_fromLocation === void 0 ? void 0 : _trip_fromLocation.title) || \"\").concat((trip === null || trip === void 0 ? void 0 : (_trip_fromLocation1 = trip.fromLocation) === null || _trip_fromLocation1 === void 0 ? void 0 : _trip_fromLocation1.title) && (trip === null || trip === void 0 ? void 0 : (_trip_toLocation = trip.toLocation) === null || _trip_toLocation === void 0 ? void 0 : _trip_toLocation.title) ? \" -> \" : \"\").concat((trip === null || trip === void 0 ? void 0 : trip.arrive) ? convertTimeFormat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(trip === null || trip === void 0 ? void 0 : trip.arrive).format(\"HH:mm \")) : (trip === null || trip === void 0 ? void 0 : trip.arriveTime) ? convertTimeFormat(trip === null || trip === void 0 ? void 0 : trip.arriveTime) + \" - \" : \"- No arrival time \").concat((trip === null || trip === void 0 ? void 0 : (_trip_toLocation1 = trip.toLocation) === null || _trip_toLocation1 === void 0 ? void 0 : _trip_toLocation1.title) || \"\").concat(!(trip === null || trip === void 0 ? void 0 : (_trip_fromLocation2 = trip.fromLocation) === null || _trip_fromLocation2 === void 0 ? void 0 : _trip_fromLocation2.title) && !(trip === null || trip === void 0 ? void 0 : (_trip_toLocation2 = trip.toLocation) === null || _trip_toLocation2 === void 0 ? void 0 : _trip_toLocation2.title) ? \" - \" : \" \")\n            };\n        });\n        const scheduledTrips = (tripReportSchedules || []).map((schedule)=>{\n            // Check if this schedule has already been created as a trip\n            const isAlreadyCreated = existingTrips.some((trip)=>trip.tripReportScheduleID === schedule.id);\n            return {\n                ...schedule,\n                isScheduled: true,\n                isCreated: isAlreadyCreated,\n                sortTime: schedule.departTime || \"00:00:00\",\n                displayText: \"\".concat(schedule.departTime, \" - \").concat(schedule.arriveTime, \" | \").concat(schedule.fromLocation.title, \" → \").concat(schedule.toLocation.title)\n            };\n        });\n        // Combine and sort by departure time\n        const combined = [\n            ...existingTrips,\n            ...scheduledTrips.filter((s)=>!s.isCreated)\n        ];\n        return combined.sort((a, b)=>{\n            const timeA = a.sortTime || \"00:00:00\";\n            const timeB = b.sortTime || \"00:00:00\";\n            return timeA.localeCompare(timeB);\n        });\n    }, [\n        tripReport,\n        tripReportSchedules\n    ]);\n    // Handle creating a trip from a scheduled item\n    const handleCreateFromSchedule = (scheduleItem)=>{\n        setSelectedTripReportSchedule(scheduleItem);\n        const input = {\n            tripReportScheduleID: scheduleItem.id,\n            departTime: scheduleItem.departTime,\n            arriveTime: scheduleItem.arriveTime,\n            fromLocationID: scheduleItem.fromLocationID,\n            toLocationID: scheduleItem.toLocationID,\n            logBookEntryID: logentryID\n        };\n        doCreateTripReport(input, true) // true indicates this is a scheduled trip\n        ;\n    };\n    // Implement confirmDeletetrip using  AlertNew component to confirm trip deletion\n    const confirmDeleteTrip = (item)=>{\n        if (item) {\n            setDeleteTripItem(item);\n            setDeleteTripConfirmation(true);\n        }\n    };\n    const [deleteTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_40__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.DeleteTripReport_LogBookEntrySections, {\n        onCompleted: ()=>{\n            // const data = response.deleteTripReport_LogBookEntrySection\n            // updateTripReport({\n            //     id: data,\n            //     key: 'archived',\n            //     value: true,\n            // })\n            updateTripReport({\n                id: [\n                    ...tripReport.filter((trip)=>trip.id !== deleteTripItem.id).map((trip)=>trip.id)\n                ]\n            });\n            setDeleteTripConfirmation(false);\n            setDeleteTripItem(false);\n        },\n        onError: (error)=>{\n            console.error(\"Error deleting trip report\", error);\n            setDeleteTripConfirmation(false);\n            setDeleteTripItem(false);\n        }\n    });\n    const handleDeleteTrip = async ()=>{\n        if (deleteTripItem) {\n            if (!offline) {\n                await deleteTripReport_LogBookEntrySection({\n                    variables: {\n                        ids: [\n                            deleteTripItem.id\n                        ]\n                    }\n                });\n            } else {\n                await tripReportModel.save({\n                    id: deleteTripItem.id,\n                    archived: true\n                });\n            }\n            setDeleteTripConfirmation(false);\n            setDeleteTripItem(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex xs:flex-row flex-col xs:justify-between gap-2 xs:gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__.Combobox, {\n                            options: tripScheduleServices,\n                            value: tripScheduleServices.find((option)=>option.value === selectedTripScheduleServiceID) || null,\n                            onChange: (e)=>{\n                                if (e) {\n                                    setSelectedTripScheduleServiceID(e.value);\n                                    loadTripReportSchedules(e.value);\n                                } else {\n                                    setSelectedTripScheduleServiceID(null);\n                                    setTripReportSchedules([]);\n                                    setShowNextTrips(false);\n                                }\n                            },\n                            placeholder: \"Select Trip Schedule Service\",\n                            disabled: locked\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1197,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                            onClick: handleCustomTrip,\n                            variant: \"primary\",\n                            disabled: locked,\n                            children: \"Add Non-Scheduled Trip\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1219,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                    lineNumber: 1196,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1195,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: \"This section covers the logbook entry. This can be made up of a single trip or many over the course of the voyage.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1250,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: combinedTripData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_22__.Accordion, {\n                    type: \"single\",\n                    collapsible: true,\n                    value: accordionValue,\n                    onValueChange: (value)=>{\n                        setAccordionValue(value);\n                        // If we're closing the accordion, reset the state\n                        if (value === \"\") {\n                            setSelectedTab(0);\n                            setOpenTripModal(false);\n                            setCurrentTrip(false);\n                            setSelectedTripReportSchedule(null);\n                        } else {\n                            // Find the selected item from combined data\n                            const selectedItem = combinedTripData.find((item)=>item.id.toString() === value);\n                            if (selectedItem) {\n                                if (selectedItem.isScheduled && !selectedItem.isCreated) {\n                                    // This is a scheduled trip that hasn't been created yet\n                                    // Don't set currentTrip, just expand the accordion\n                                    setSelectedTab(0);\n                                    setOpenTripModal(false);\n                                    setCurrentTrip(false);\n                                    setSelectedTripReportSchedule(null);\n                                } else {\n                                    // This is an existing trip or a created scheduled trip\n                                    setSelectedTab(selectedItem.id);\n                                    setOpenTripModal(true);\n                                    setSignatureKey(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_7___default()());\n                                    currentTripRef.current = selectedItem;\n                                    setCurrentTrip(selectedItem);\n                                    setSelectedTripReportSchedule(null);\n                                    // Initialize signature data if available\n                                    if (selectedItem.sectionSignature) {\n                                        setSignature(selectedItem.sectionSignature.signatureData || \"\");\n                                    } else {\n                                        setSignature(\"\");\n                                    }\n                                    setRiskBufferEvDgr(selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.dangerousGoodsChecklist);\n                                    setOpenTripStartRiskAnalysis(false);\n                                    setAllDangerousGoods(false);\n                                    setCurrentStopEvent(false);\n                                    setCurrentEventTypeEvent(false);\n                                    setSelectedRowEvent(false);\n                                    setDisplayDangerousGoods((selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.enableDGR) === true);\n                                    setDisplayDangerousGoodsSailing((selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.designatedDangerousGoodsSailing) === true);\n                                    setDisplayDangerousGoodsPvpd(false);\n                                    setDisplayDangerousGoodsPvpdSailing(null);\n                                    setAllPVPDDangerousGoods(false);\n                                    setSelectedDGRPVPD(false);\n                                    setTripReport_Stops(false);\n                                }\n                            }\n                        }\n                    },\n                    children: combinedTripData.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_22__.AccordionItem, {\n                            value: item.id.toString(),\n                            id: \"combined-trip-\".concat(item.id),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_22__.AccordionTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.displayText\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1339,\n                                                        columnNumber: 45\n                                                    }, this),\n                                                    item.isScheduled && !item.isCreated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\",\n                                                        children: \"Scheduled\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                        lineNumber: 1342,\n                                                        columnNumber: 53\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1338,\n                                                columnNumber: 41\n                                            }, this),\n                                            item.isScheduled && !item.isCreated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleCreateFromSchedule(item);\n                                                },\n                                                iconLeft: _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_41__[\"default\"],\n                                                size: \"sm\",\n                                                className: \"ml-2\",\n                                                disabled: locked,\n                                                children: \"Create Trip\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1349,\n                                                columnNumber: 49\n                                            }, this),\n                                            !item.isScheduled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    confirmDeleteTrip(item);\n                                                },\n                                                iconLeft: _barrel_optimize_names_ArrowLeft_Check_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_42__[\"default\"],\n                                                size: \"sm\",\n                                                className: \"ml-2\",\n                                                variant: \"destructive\",\n                                                disabled: locked,\n                                                children: \"Delete Trip\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                                lineNumber: 1364,\n                                                columnNumber: 45\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                        lineNumber: 1337,\n                                        columnNumber: 37\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                    lineNumber: 1336,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_22__.AccordionContent, {\n                                    className: \"px-5 sm:px-10\",\n                                    children: !item.isScheduled || item.isCreated ? currentTrip && currentTrip.id === item.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TripReportAccordionContent, {\n                                        currentTrip: currentTrip,\n                                        offline: offline,\n                                        tripReport: tripReport,\n                                        updateTripReport: updateTripReport,\n                                        updateTripReport_LogBookEntrySection: updateTripReport_LogBookEntrySection,\n                                        currentTripRef: currentTripRef,\n                                        locations: locations,\n                                        locked: locked,\n                                        edit_tripReport: edit_tripReport,\n                                        vessel: vessel,\n                                        crewMembers: crewMembers,\n                                        logBookConfig: logBookConfig,\n                                        client: client,\n                                        canCarryVehicles: canCarryVehicles,\n                                        canCarryDangerousGoods: canCarryDangerousGoods,\n                                        selectedDGR: selectedDGR,\n                                        displayDangerousGoods: displayDangerousGoods,\n                                        setDisplayDangerousGoods: setDisplayDangerousGoods,\n                                        displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                                        setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                                        allDangerousGoods: allDangerousGoods,\n                                        setAllDangerousGoods: setAllDangerousGoods,\n                                        logBookStartDate: logBookStartDate,\n                                        masterID: masterID,\n                                        vessels: vessels,\n                                        setSelectedRowEvent: setSelectedRowEvent,\n                                        setCurrentEventTypeEvent: setCurrentEventTypeEvent,\n                                        setCurrentStopEvent: setCurrentStopEvent,\n                                        currentEventTypeEvent: currentEventTypeEvent,\n                                        currentStopEvent: currentStopEvent,\n                                        tripReport_Stops: tripReport_Stops,\n                                        setTripReport_Stops: setTripReport_Stops,\n                                        displayDangerousGoodsPvpd: displayDangerousGoodsPvpd,\n                                        setDisplayDangerousGoodsPvpd: setDisplayDangerousGoodsPvpd,\n                                        displayDangerousGoodsPvpdSailing: displayDangerousGoodsPvpdSailing,\n                                        setDisplayDangerousGoodsPvpdSailing: setDisplayDangerousGoodsPvpdSailing,\n                                        allPVPDDangerousGoods: allPVPDDangerousGoods,\n                                        setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                                        selectedDGRPVPD: selectedDGRPVPD,\n                                        setSelectedDGRPVPD: setSelectedDGRPVPD,\n                                        fuelLogs: fuelLogs,\n                                        comment: comment,\n                                        setComment: setComment,\n                                        displayFieldTripLog: displayFieldTripLog,\n                                        signatureKey: signatureKey,\n                                        signature: signature,\n                                        setSignature: setSignature,\n                                        handleCancel: handleCancel,\n                                        handleSave: handleSave\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                        lineNumber: 1384,\n                                        columnNumber: 45\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-muted-foreground\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: 'Click the \"Create Trip\" button above to create this scheduled trip and access the trip details.'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                            lineNumber: 1494,\n                                            columnNumber: 45\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                        lineNumber: 1493,\n                                        columnNumber: 41\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                                    lineNumber: 1379,\n                                    columnNumber: 33\n                                }, this)\n                            ]\n                        }, \"combined-trip-\".concat(item.id, \"-\").concat(item.isScheduled ? \"scheduled\" : \"existing\"), true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                            lineNumber: 1332,\n                            columnNumber: 29\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                    lineNumber: 1257,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1255,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_23__.AlertDialogNew, {\n                openDialog: deleteTripConfirmation,\n                setOpenDialog: setDeleteTripConfirmation,\n                // handleCreate={handleDeleteTrip}\n                title: \"Delete Trip\",\n                description: \"Are you sure you want to delete this trip? This action cannot be undone.\",\n                cancelText: \"Cancel\",\n                destructiveActionText: \"Delete\",\n                handleDestructiveAction: handleDeleteTrip,\n                showDestructiveAction: true,\n                variant: \"danger\",\n                actionText: \"Delete\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log.tsx\",\n                lineNumber: 1509,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(TripLog, \"fYkeOEMgzU8Y2Zz76B70QizYCGk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_16__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_39__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_39__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_40__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_40__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_40__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_39__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_39__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_40__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_40__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_40__.useMutation\n    ];\n});\n_c1 = TripLog;\nvar _c, _c1;\n$RefreshReg$(_c, \"TripReportAccordionContent\");\n$RefreshReg$(_c1, \"TripLog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/trip-log.tsx\n"));

/***/ })

});