"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/trip-log-pob.tsx":
/*!*********************************************!*\
  !*** ./src/app/ui/logbook/trip-log-pob.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ POB; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_offline_models_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/offline/models/client */ \"(app-pages-browser)/./src/app/offline/models/client.js\");\n/* harmony import */ var _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/offline/models/tripReport_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/tripReport_LogBookEntrySection.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../../../utils/responsiveLabel */ \"(app-pages-browser)/./utils/responsiveLabel.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction POB(param) {\n    let { currentTrip, updateTripReport, tripReport, vessel, crewMembers, logBookConfig, masterTerm = \"Master\", offline = false, setTab } = param;\n    _s();\n    const clientModel = new _app_offline_models_client__WEBPACK_IMPORTED_MODULE_4__[\"default\"]();\n    const tripReportModel = new _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_5__[\"default\"]();\n    const [client, setClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [pob, setPOB] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalGuests, setTotalGuests] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [paxJoined, setPaxJoined] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [vehicleJoined, setVehicleJoined] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints)();\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_3__.getOneClient)(setClient);\n    }\n    const offlineMount = async ()=>{\n        var _localStorage_getItem;\n        // getOneClient(setClient)\n        const client = await clientModel.getById(+((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0));\n        setClient(client);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            offlineMount();\n        }\n    }, [\n        offline\n    ]);\n    const handlePOBChange = async (persons)=>{\n        const inputVal = persons.target.value;\n        const totalValue = Number(inputVal) || 0;\n        const pobValue = totalValue - paxJoined;\n        if (offline) {\n            // updateTripReport_LogBookEntrySection\n            const data = await tripReportModel.save({\n                id: currentTrip.id,\n                pob: pobValue\n            });\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    data.id\n                ],\n                currentTripID: currentTrip.id,\n                key: \"pob\",\n                value: pobValue\n            });\n        } else {\n            updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: currentTrip.id,\n                        pob: pobValue\n                    }\n                }\n            });\n        }\n        setPOB(pobValue);\n    };\n    const handlePOBValueChange = (persons)=>{\n        const inputVal = persons.target.value;\n        setInputValue(inputVal);\n        const totalValue = Number(inputVal) || 0;\n        const pobValue = totalValue - paxJoined;\n        setPOB(pobValue);\n    };\n    const handleIncrement = async ()=>{\n        const currentValue = Number(inputValue) || 0;\n        const newValue = currentValue + 1;\n        setInputValue(newValue.toString());\n        const pobValue = newValue - paxJoined;\n        if (offline) {\n            const data = await tripReportModel.save({\n                id: currentTrip.id,\n                pob: pobValue\n            });\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    data.id\n                ],\n                currentTripID: currentTrip.id,\n                key: \"pob\",\n                value: pobValue\n            });\n        } else {\n            updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: currentTrip.id,\n                        pob: pobValue\n                    }\n                }\n            });\n        }\n        setPOB(pobValue);\n    };\n    const handleDecrement = async ()=>{\n        const currentValue = Number(inputValue) || 0;\n        const minValue = isNaN(paxJoined) ? 0 : paxJoined;\n        const newValue = Math.max(minValue, currentValue - 1);\n        setInputValue(newValue.toString());\n        const pobValue = newValue - paxJoined;\n        if (offline) {\n            const data = await tripReportModel.save({\n                id: currentTrip.id,\n                pob: pobValue\n            });\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    data.id\n                ],\n                currentTripID: currentTrip.id,\n                key: \"pob\",\n                value: pobValue\n            });\n        } else {\n            updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: currentTrip.id,\n                        pob: pobValue\n                    }\n                }\n            });\n        }\n        setPOB(pobValue);\n    };\n    const [updateTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_12__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateTripReport_LogBookEntrySection, {\n        onCompleted: (data)=>{},\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const setGuests = ()=>{\n        var _currentTrip_tripEvents;\n        let totalGuests = 0;\n        const supernumeraries = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.filter((event)=>{\n            return event.eventCategory === \"Supernumerary\";\n        });\n        if ((supernumeraries === null || supernumeraries === void 0 ? void 0 : supernumeraries.length) > 0) {\n            supernumeraries.forEach((s)=>{\n                var _s_supernumerary;\n                totalGuests += ((_s_supernumerary = s.supernumerary) === null || _s_supernumerary === void 0 ? void 0 : _s_supernumerary.totalGuest) || 0;\n            });\n        }\n        setTotalGuests(totalGuests);\n        return totalGuests;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentTrip) {\n            var _currentTrip_tripReport_Stops, _currentTrip_tripReport_Stops1;\n            var _currentTrip_pob;\n            const pobValue = Number((_currentTrip_pob = currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.pob) !== null && _currentTrip_pob !== void 0 ? _currentTrip_pob : 0);\n            setPOB(pobValue);\n            setGuests();\n            var _currentTrip_tripReport_Stops_nodes_reduce;\n            const paxJoinedValue = (_currentTrip_tripReport_Stops_nodes_reduce = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops === void 0 ? void 0 : _currentTrip_tripReport_Stops.nodes.reduce((acc, stop)=>{\n                return acc + stop.paxJoined - stop.paxDeparted;\n            }, 0)) !== null && _currentTrip_tripReport_Stops_nodes_reduce !== void 0 ? _currentTrip_tripReport_Stops_nodes_reduce : 0;\n            var _currentTrip_tripReport_Stops_nodes_reduce1;\n            const vehicleJoinedValue = (_currentTrip_tripReport_Stops_nodes_reduce1 = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops1 = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops1 === void 0 ? void 0 : _currentTrip_tripReport_Stops1.nodes.reduce((acc, stop)=>{\n                return acc + stop.vehiclesJoined - stop.vehiclesDeparted;\n            }, 0)) !== null && _currentTrip_tripReport_Stops_nodes_reduce1 !== void 0 ? _currentTrip_tripReport_Stops_nodes_reduce1 : 0;\n            setPaxJoined(paxJoinedValue);\n            setVehicleJoined(vehicleJoinedValue);\n            // Update input value to reflect the total (pob + paxJoined)\n            const totalInputValue = pobValue + paxJoinedValue;\n            setInputValue(totalInputValue.toString());\n        }\n    }, [\n        currentTrip\n    ]);\n    const crewLength = ()=>{\n        if (!crewMembers || !Array.isArray(crewMembers)) {\n            return 0;\n        }\n        const count = crewMembers.filter((member)=>member.crewMemberID > 0 && member.punchOut === null).length;\n        return count;\n    };\n    const displayField = (fieldName)=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents, _dailyChecks__customisedComponentFields, _dailyChecks_;\n        const dailyChecks = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"EventType_LogBookComponent\");\n        if ((dailyChecks === null || dailyChecks === void 0 ? void 0 : dailyChecks.length) > 0 && ((_dailyChecks_ = dailyChecks[0]) === null || _dailyChecks_ === void 0 ? void 0 : (_dailyChecks__customisedComponentFields = _dailyChecks_.customisedComponentFields) === null || _dailyChecks__customisedComponentFields === void 0 ? void 0 : _dailyChecks__customisedComponentFields.nodes.filter((field)=>field.fieldName === fieldName && field.status !== \"Off\").length) > 0) {\n            return true;\n        }\n        return false;\n    };\n    const totalPOB = pob + crewLength() + paxJoined + totalGuests;\n    const isOverCapacity = (vessel === null || vessel === void 0 ? void 0 : vessel.maxPOB) < totalPOB;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                label: \"Crew (Incl. \".concat(masterTerm, \")\"),\n                position: (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_11__.getResponsiveLabel)(bp.phablet, \"left\", \"top\"),\n                labelClassName: \"xs:w-[200px] \",\n                htmlFor: \"crew\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"items-center space-x-2.5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                            variant: \"outline\",\n                            children: crewLength()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log-pob.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                            className: \"border\",\n                            onClick: ()=>setTab === null || setTab === void 0 ? void 0 : setTab(\"crew\"),\n                            children: \"Add crew\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log-pob.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log-pob.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log-pob.tsx\",\n                lineNumber: 262,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                label: \"Passengers on board\",\n                htmlFor: \"pob\",\n                position: (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_11__.getResponsiveLabel)(bp.phablet, \"left\", \"top\"),\n                labelClassName: \"xs:w-[200px] \",\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2.5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                            className: \" rounded-full size-9\",\n                            variant: \"primaryOutline\",\n                            onClick: handleDecrement,\n                            iconLeft: _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log-pob.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                            id: \"pob\",\n                            name: \"pob\",\n                            type: \"number\",\n                            value: inputValue,\n                            className: \"w-20\",\n                            required: true,\n                            min: isNaN(paxJoined) ? 0 : paxJoined,\n                            onBlur: handlePOBChange,\n                            onChange: handlePOBValueChange\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log-pob.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                            className: \" rounded-full size-9\",\n                            variant: \"primaryOutline\",\n                            onClick: handleIncrement,\n                            iconLeft: _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log-pob.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log-pob.tsx\",\n                    lineNumber: 282,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log-pob.tsx\",\n                lineNumber: 276,\n                columnNumber: 13\n            }, this),\n            displayField(\"EventSupernumerary\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                label: \"Supernumerary\",\n                htmlFor: \"supernumerary\",\n                position: (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_11__.getResponsiveLabel)(bp.phablet, \"left\", \"top\"),\n                labelClassName: \"xs:w-[200px] \",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                            variant: \"outline\",\n                            children: isNaN(totalGuests) ? 0 : totalGuests\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log-pob.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 25\n                        }, this),\n                        \" \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"total guests\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log-pob.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log-pob.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log-pob.tsx\",\n                lineNumber: 311,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                label: \"Total P.O.B:\",\n                htmlFor: \"totalPob\",\n                position: (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_11__.getResponsiveLabel)(bp.phablet, \"left\", \"top\"),\n                labelClassName: \"xs:w-[200px] \",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                    variant: isOverCapacity ? \"destructive\" : \"success\",\n                    children: isNaN(totalPOB) ? 0 : totalPOB\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log-pob.tsx\",\n                    lineNumber: 332,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log-pob.tsx\",\n                lineNumber: 327,\n                columnNumber: 13\n            }, this),\n            isOverCapacity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-destructive text-sm\",\n                children: \"WARNING: Your total P.O.B exceeds your max P.O.B as setup in your vessel config\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log-pob.tsx\",\n                lineNumber: 338,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\trip-log-pob.tsx\",\n        lineNumber: 260,\n        columnNumber: 9\n    }, this);\n}\n_s(POB, \"0Gl6ONDy5AInn5XOpmwo0hDndPs=\", false, function() {\n    return [\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_12__.useMutation\n    ];\n});\n_c = POB;\nvar _c;\n$RefreshReg$(_c, \"POB\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/trip-log-pob.tsx\n"));

/***/ })

});