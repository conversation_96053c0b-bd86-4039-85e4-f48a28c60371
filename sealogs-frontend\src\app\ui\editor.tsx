import React, { useMemo } from 'react'
import dynamic from 'next/dynamic'
import 'react-quill/dist/quill.snow.css'
import { cn } from '../lib/utils'
import { useBreakpoints } from '@/components/hooks/useBreakpoints'

const QuillEditor = dynamic(() => import('react-quill'), { ssr: false })

interface EditorProps {
    content?: string | boolean // Allow boolean for backward compatibility
    placeholder?: string
    handleEditorChange: (content: string) => void
    handleEditorBlur?: (() => void) | ((value: any) => Promise<void>) // Allow both sync and async handlers
    className?: string
    id?: string
    disabled?: boolean
    name?: string
    autoResize?: boolean // Add autoResize prop for backward compatibility
}

export default function Editor({
    content,
    placeholder,
    handleEditorChange,
    handleEditorBlur,
    className,
    id,
    disabled,
    name,
    autoResize, // Accept but ignore for backward compatibility
}: EditorProps) {
    // Use existing breakpoints hook from the codebase
    const bp = useBreakpoints()
    const isMobile = !bp['tablet-md'] // tablet-md is 768px, so this matches the original logic

    // Ensure content is always a string
    const editorContent =
        typeof content === 'string' ? content : content ? '' : ''

    const desktopToolbar = [
        [{ header: [1, 2, 3, false] }],
        ['bold', 'italic', 'underline'],
        [{ list: 'ordered' }, { list: 'bullet' }],
        ['link', 'image'],
        [{ align: [] }, { color: [] }],
        ['clean'],
    ]

    // Enhanced mobile toolbar with more options than before
    const mobileToolbar = [
        [{ header: [1, 2, false] }],
        ['bold', 'italic', 'underline'],
        [{ list: 'ordered' }, { list: 'bullet' }],
        ['link'],
        ['clean'],
    ]

    // Use useMemo to prevent unnecessary re-renders and dynamic toolbar switching
    const quillModules = useMemo(
        () => ({
            toolbar: isMobile ? mobileToolbar : desktopToolbar,
        }),
        [isMobile],
    )

    const quillFormats = [
        'header',
        'bold',
        'italic',
        'underline',
        'strike',
        'blockquote',
        'list',
        'bullet',
        'link',
        'image',
        'align',
        'color',
        'code-block',
    ]

    return (
        <QuillEditor
            value={editorContent}
            placeholder={placeholder}
            onChange={handleEditorChange}
            onBlur={handleEditorBlur}
            modules={quillModules}
            formats={quillFormats}
            readOnly={disabled}
            id={id}
            className={cn(
                'w-full min-h-60 mb-2 bg-card border text-input border-border rounded-lg',
                // Responsive improvements
                'tablet-md:min-h-40', // Smaller height on larger screens
                '[&_.ql-toolbar]:flex-wrap', // Allow toolbar to wrap on small screens
                '[&_.ql-toolbar]:gap-1', // Add spacing between toolbar items
                '[&_.ql-editor]:text-sm tablet-md:[&_.ql-editor]:text-base', // Responsive text size
                '[&_.ql-toolbar_.ql-formats]:flex-wrap', // Allow format groups to wrap
                '[&_.ql-toolbar_.ql-formats]:gap-1', // Add gap between format groups
                // Better mobile touch targets
                '[&_.ql-toolbar_button]:min-h-[44px] [&_.ql-toolbar_button]:min-w-[44px]',
                '[&_.ql-toolbar_.ql-picker]:min-h-[44px]',
                className,
            )}
        />
    )
}
