import React, { useMemo } from 'react'
import dynamic from 'next/dynamic'
import 'react-quill/dist/quill.snow.css'
import { cn } from '../lib/utils'
import { useBreakpoints } from '@/components/hooks/useBreakpoints'

const QuillEditor = dynamic(() => import('react-quill'), { ssr: false })

interface EditorProps {
    content?: string | boolean // Allow boolean for backward compatibility
    placeholder?: string
    handleEditorChange: (content: string) => void
    handleEditorBlur?: (() => void) | ((value: any) => Promise<void>) // Allow both sync and async handlers
    className?: string
    id?: string
    disabled?: boolean
    name?: string
    autoResize?: boolean // Add autoResize prop for backward compatibility
}

export default function Editor({
    content,
    placeholder,
    handleEditorChange,
    handleEditorBlur,
    className,
    id,
    disabled,
    name,
    autoResize, // Accept but ignore for backward compatibility
}: EditorProps) {
    // Use existing breakpoints hook from the codebase
    const bp = useBreakpoints()
    const isMobile = !bp['tablet-md'] // tablet-md is 768px, so this matches the original logic

    // Ensure content is always a string
    const editorContent =
        typeof content === 'string' ? content : content ? '' : ''

    const desktopToolbar = [
        [{ header: [1, 2, 3, false] }],
        ['bold', 'italic', 'underline'],
        [{ list: 'ordered' }, { list: 'bullet' }],
        ['link', 'image'],
        [{ align: [] }, { color: [] }],
        ['clean'],
    ]

    // Enhanced mobile toolbar with more options than before
    const mobileToolbar = [
        [{ header: [1, 2, false] }],
        ['bold', 'italic', 'underline'],
        [{ list: 'ordered' }, { list: 'bullet' }],
        ['link'],
        ['clean'],
    ]

    // Use useMemo to prevent unnecessary re-renders and dynamic toolbar switching
    const quillModules = useMemo(
        () => ({
            toolbar: isMobile ? mobileToolbar : desktopToolbar,
        }),
        [isMobile],
    )

    const quillFormats = [
        'header',
        'bold',
        'italic',
        'underline',
        'strike',
        'blockquote',
        'list',
        'bullet',
        'link',
        'image',
        'align',
        'color',
        'code-block',
    ]

    return (
        <QuillEditor
            value={editorContent}
            placeholder={placeholder}
            onChange={handleEditorChange}
            onBlur={handleEditorBlur}
            modules={quillModules}
            formats={quillFormats}
            readOnly={disabled}
            id={id}
            className={cn(
                'w-full min-h-60 mb-2 bg-card border text-input border-border rounded-lg',
                // Responsive improvements
                'tablet-md:min-h-40', // Smaller height on larger screens
                '[&_.ql-toolbar]:flex-wrap', // Allow toolbar to wrap on small screens
                '[&_.ql-editor]:text-sm tablet-md:[&_.ql-editor]:text-base', // Responsive text size
                // Compact toolbar styling with proper alignment
                '[&_.ql-toolbar]:py-2 [&_.ql-toolbar]:px-3', // Reduce toolbar padding
                '[&_.ql-toolbar]:flex [&_.ql-toolbar]:items-center', // Center align toolbar items
                '[&_.ql-toolbar_.ql-formats]:flex [&_.ql-toolbar_.ql-formats]:items-center', // Center align format groups
                '[&_.ql-toolbar_.ql-formats]:gap-1 [&_.ql-toolbar_.ql-formats]:mr-2', // Add spacing between format groups
                // Button styling
                '[&_.ql-toolbar_button]:h-8 [&_.ql-toolbar_button]:w-8', // Smaller button size
                '[&_.ql-toolbar_button]:flex [&_.ql-toolbar_button]:items-center [&_.ql-toolbar_button]:justify-center', // Center button content
                '[&_.ql-toolbar_button]:text-sm', // Smaller button text/icons
                // Picker styling
                '[&_.ql-toolbar_.ql-picker]:h-8', // Smaller picker height
                '[&_.ql-toolbar_.ql-picker-label]:text-sm', // Smaller picker text
                '[&_.ql-toolbar_.ql-picker-label]:py-1 [&_.ql-toolbar_.ql-picker-label]:px-2', // Reduce picker padding
                '[&_.ql-toolbar_.ql-picker-label]:flex [&_.ql-toolbar_.ql-picker-label]:items-center', // Center picker text
                // Better mobile touch targets (only on mobile)
                '[&_.ql-toolbar_button]:tiny:h-9 [&_.ql-toolbar_button]:tiny:w-9', // Slightly larger on tiny screens
                className,
            )}
        />
    )
}
