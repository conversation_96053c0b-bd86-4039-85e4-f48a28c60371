"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/ui/editor.tsx":
/*!*******************************!*\
  !*** ./src/app/ui/editor.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Editor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var react_quill_dist_quill_snow_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-quill/dist/quill.snow.css */ \"(app-pages-browser)/./node_modules/.pnpm/react-quill@2.0.0_react-dom_e84ee3d05de24e5925e3d303aba3a8e9/node_modules/react-quill/dist/quill.snow.css\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst QuillEditor = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_pnpm_react-quill_2_0_0_react-dom_e84ee3d05de24e5925e3d303aba3-5c8f62\").then(__webpack_require__.t.bind(__webpack_require__, /*! react-quill */ \"(app-pages-browser)/./node_modules/.pnpm/react-quill@2.0.0_react-dom_e84ee3d05de24e5925e3d303aba3a8e9/node_modules/react-quill/lib/index.js\", 23)), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\ui\\\\editor.tsx -> \" + \"react-quill\"\n        ]\n    },\n    ssr: false\n});\n_c = QuillEditor;\nfunction Editor(param) {\n    let { content, placeholder, handleEditorChange, handleEditorBlur, className, id, disabled, name, autoResize } = param;\n    _s();\n    // Use existing breakpoints hook from the codebase\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_5__.useBreakpoints)();\n    const isMobile = !bp[\"tablet-md\"] // tablet-md is 768px, so this matches the original logic\n    ;\n    // Ensure content is always a string\n    const editorContent = typeof content === \"string\" ? content : content ? \"\" : \"\";\n    const desktopToolbar = [\n        [\n            {\n                header: [\n                    1,\n                    2,\n                    3,\n                    false\n                ]\n            }\n        ],\n        [\n            \"bold\",\n            \"italic\",\n            \"underline\"\n        ],\n        [\n            {\n                list: \"ordered\"\n            },\n            {\n                list: \"bullet\"\n            }\n        ],\n        [\n            \"link\",\n            \"image\"\n        ],\n        [\n            {\n                align: []\n            },\n            {\n                color: []\n            }\n        ],\n        [\n            \"clean\"\n        ]\n    ];\n    // Enhanced mobile toolbar with more options than before\n    const mobileToolbar = [\n        [\n            {\n                header: [\n                    1,\n                    2,\n                    false\n                ]\n            }\n        ],\n        [\n            \"bold\",\n            \"italic\",\n            \"underline\"\n        ],\n        [\n            {\n                list: \"ordered\"\n            },\n            {\n                list: \"bullet\"\n            }\n        ],\n        [\n            \"link\"\n        ],\n        [\n            \"clean\"\n        ]\n    ];\n    // Use useMemo to prevent unnecessary re-renders and dynamic toolbar switching\n    const quillModules = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            toolbar: isMobile ? mobileToolbar : desktopToolbar\n        }), [\n        isMobile\n    ]);\n    const quillFormats = [\n        \"header\",\n        \"bold\",\n        \"italic\",\n        \"underline\",\n        \"strike\",\n        \"blockquote\",\n        \"list\",\n        \"bullet\",\n        \"link\",\n        \"image\",\n        \"align\",\n        \"color\",\n        \"code-block\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuillEditor, {\n        value: editorContent,\n        placeholder: placeholder,\n        onChange: handleEditorChange,\n        onBlur: handleEditorBlur,\n        modules: quillModules,\n        formats: quillFormats,\n        readOnly: disabled,\n        id: id,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"w-full min-h-60 mb-2 bg-card border text-input border-border rounded-lg\", // Responsive improvements\n        \"tablet-md:min-h-40\", \"[&_.ql-toolbar]:flex-wrap\", \"[&_.ql-editor]:text-sm tablet-md:[&_.ql-editor]:text-base\", // Compact toolbar styling with proper alignment\n        \"[&_.ql-toolbar]:py-2 [&_.ql-toolbar]:px-3\", \"[&_.ql-toolbar]:flex [&_.ql-toolbar]:items-center\", \"[&_.ql-toolbar_.ql-formats]:flex [&_.ql-toolbar_.ql-formats]:items-center\", \"[&_.ql-toolbar_.ql-formats]:gap-1 [&_.ql-toolbar_.ql-formats]:mr-2\", // Button styling\n        \"[&_.ql-toolbar_button]:h-8 [&_.ql-toolbar_button]:w-8\", \"[&_.ql-toolbar_button]:flex [&_.ql-toolbar_button]:items-center [&_.ql-toolbar_button]:justify-center\", \"[&_.ql-toolbar_button]:text-sm\", // Picker styling\n        \"[&_.ql-toolbar_.ql-picker]:h-8\", \"[&_.ql-toolbar_.ql-picker-label]:text-sm\", \"[&_.ql-toolbar_.ql-picker-label]:py-1 [&_.ql-toolbar_.ql-picker-label]:px-2\", \"[&_.ql-toolbar_.ql-picker-label]:flex [&_.ql-toolbar_.ql-picker-label]:items-center\", // Better mobile touch targets (only on mobile)\n        \"[&_.ql-toolbar_button]:tiny:h-9 [&_.ql-toolbar_button]:tiny:w-9\", className)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\editor.tsx\",\n        lineNumber: 83,\n        columnNumber: 9\n    }, this);\n}\n_s(Editor, \"ljpbraJbyBcrRHiP7JzyubTY4eA=\", false, function() {\n    return [\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_5__.useBreakpoints\n    ];\n});\n_c1 = Editor;\nvar _c, _c1;\n$RefreshReg$(_c, \"QuillEditor\");\n$RefreshReg$(_c1, \"Editor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/editor.tsx\n"));

/***/ })

});