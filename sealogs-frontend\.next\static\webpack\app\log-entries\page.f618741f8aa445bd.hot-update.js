"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/components/trip-comments.tsx":
/*!*********************************************************!*\
  !*** ./src/app/ui/logbook/components/trip-comments.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TripComments; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/offline/models/tripReport_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/tripReport_LogBookEntrySection.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction TripComments(param) {\n    let { currentTrip, updateTripReport, tripReport, setCommentField, offline = false } = param;\n    var _tripReport_find_comment, _tripReport_find, _tripReport_find_comment1, _tripReport_find1, _tripReport_find2;\n    _s();\n    const tripReportModel = new _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_4__[\"default\"]();\n    const [comment, setComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleEditorChange = (value)=>{\n        setComment(value);\n        setCommentField(value);\n    };\n    const handleEditorBlur = async (value)=>{\n        var _tripReport_find;\n        if (comment != (tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find === void 0 ? void 0 : _tripReport_find.comment)) {\n            if (offline) {\n                const data = await tripReportModel.save({\n                    id: currentTrip.id,\n                    comment: comment\n                });\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        data.id\n                    ],\n                    currentTripID: currentTrip.id,\n                    key: \"comment\",\n                    value: comment\n                });\n            } else {\n                updateTripReport_LogBookEntrySection({\n                    variables: {\n                        input: {\n                            id: currentTrip.id,\n                            comment: comment\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [updateTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_5__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateTripReport_LogBookEntrySection, {\n        onCompleted: (data)=>{},\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (tripReport) {\n            var _tripReport_find_comment, _tripReport_find, _tripReport_find1;\n            if (comment !== (tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find === void 0 ? void 0 : (_tripReport_find_comment = _tripReport_find.comment) === null || _tripReport_find_comment === void 0 ? void 0 : _tripReport_find_comment.length) > 0 ? tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find1 = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find1 === void 0 ? void 0 : _tripReport_find1.comment : \"<p><br></p>\") {\n                var _tripReport_find_comment1, _tripReport_find2, _tripReport_find3;\n                setComment((tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find2 = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find2 === void 0 ? void 0 : (_tripReport_find_comment1 = _tripReport_find2.comment) === null || _tripReport_find_comment1 === void 0 ? void 0 : _tripReport_find_comment1.length) > 0 ? tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find3 = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find3 === void 0 ? void 0 : _tripReport_find3.comment : \"<p><br></p>\");\n            }\n        }\n    }, [\n        tripReport\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: (comment || (tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find === void 0 ? void 0 : (_tripReport_find_comment = _tripReport_find.comment) === null || _tripReport_find_comment === void 0 ? void 0 : _tripReport_find_comment.length) > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            id: \"tripReport-Content\",\n            placeholder: \"Add any comments or updates relevant to this trip. Comment if actual arrival time is significantly different to expected arrival time. You can leave overall comments for other masters in the logbook comments fields.\",\n            content: comment ? comment : (tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find1 = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find1 === void 0 ? void 0 : (_tripReport_find_comment1 = _tripReport_find1.comment) === null || _tripReport_find_comment1 === void 0 ? void 0 : _tripReport_find_comment1.length) > 0 ? tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find2 = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find2 === void 0 ? void 0 : _tripReport_find2.comment : \"<p><br></p>\",\n            className: \"w-full\",\n            handleEditorChange: handleEditorChange,\n            handleEditorBlur: handleEditorBlur\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\trip-comments.tsx\",\n            lineNumber: 99,\n            columnNumber: 17\n        }, this)\n    }, void 0, false);\n}\n_s(TripComments, \"15zRDkE0I2ss3RRMklsiBUgIBMY=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_5__.useMutation\n    ];\n});\n_c = TripComments;\nvar _c;\n$RefreshReg$(_c, \"TripComments\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/components/trip-comments.tsx\n"));

/***/ })

});